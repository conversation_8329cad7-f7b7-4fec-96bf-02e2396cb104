'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';
import { 
  Upload, 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Download, 
  Type, 
  Highlighter, 
  Square, 
  Circle, 
  ArrowRight,
  Image as ImageIcon,
  ChevronLeft,
  ChevronRight,
  FileText
} from 'lucide-react';
import FileUpload from './FileUpload';
import PDFViewer from './PDFViewer';
import Toolbar from './Toolbar';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

// Configure PDF.js worker with legacy build
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.js`;

export interface Tool {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  active: boolean;
}

export interface PDFEditorState {
  file: File | null;
  numPages: number;
  currentPage: number;
  scale: number;
  rotation: number;
  activeTool: string;
  annotations: any[];
}

const PDFEditor: React.FC = () => {
  const [state, setState] = useState<PDFEditorState>({
    file: null,
    numPages: 0,
    currentPage: 1,
    scale: 1.0,
    rotation: 0,
    activeTool: 'select',
    annotations: []
  });

  const tools: Tool[] = [
    { id: 'select', name: 'Select', icon: FileText, active: state.activeTool === 'select' },
    { id: 'text', name: 'Text', icon: Type, active: state.activeTool === 'text' },
    { id: 'highlight', name: 'Highlight', icon: Highlighter, active: state.activeTool === 'highlight' },
    { id: 'rectangle', name: 'Rectangle', icon: Square, active: state.activeTool === 'rectangle' },
    { id: 'circle', name: 'Circle', icon: Circle, active: state.activeTool === 'circle' },
    { id: 'arrow', name: 'Arrow', icon: ArrowRight, active: state.activeTool === 'arrow' },
    { id: 'image', name: 'Image', icon: ImageIcon, active: state.activeTool === 'image' },
  ];

  const handleFileUpload = useCallback((file: File) => {
    setState(prev => ({
      ...prev,
      file,
      currentPage: 1,
      numPages: 0
    }));
  }, []);

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setState(prev => ({
      ...prev,
      numPages
    }));
  }, []);

  const handleZoomIn = useCallback(() => {
    setState(prev => ({
      ...prev,
      scale: Math.min(prev.scale + 0.2, 3.0)
    }));
  }, []);

  const handleZoomOut = useCallback(() => {
    setState(prev => ({
      ...prev,
      scale: Math.max(prev.scale - 0.2, 0.5)
    }));
  }, []);

  const handleRotate = useCallback(() => {
    setState(prev => ({
      ...prev,
      rotation: (prev.rotation + 90) % 360
    }));
  }, []);

  const handlePageChange = useCallback((page: number) => {
    setState(prev => ({
      ...prev,
      currentPage: Math.max(1, Math.min(page, prev.numPages))
    }));
  }, []);

  const handleToolSelect = useCallback((toolId: string) => {
    setState(prev => ({
      ...prev,
      activeTool: toolId
    }));
  }, []);

  const handleDownload = useCallback(() => {
    if (state.file) {
      // For now, just download the original file
      // In a full implementation, this would save the edited PDF
      const url = URL.createObjectURL(state.file);
      const a = document.createElement('a');
      a.href = url;
      a.download = state.file.name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  }, [state.file]);

  if (!state.file) {
    return (
      <div className="h-screen flex flex-col">
        <header className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">PDF Editor</h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">Upload a PDF to get started</span>
            </div>
          </div>
        </header>
        <main className="flex-1 flex items-center justify-center">
          <FileUpload onFileUpload={handleFileUpload} />
        </main>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">PDF Editor</h1>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">
              {state.file.name} - Page {state.currentPage} of {state.numPages}
            </span>
            <button
              onClick={handleDownload}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Download size={16} />
              <span>Download</span>
            </button>
          </div>
        </div>
      </header>

      <div className="flex-1 flex">
        <Toolbar 
          tools={tools}
          onToolSelect={handleToolSelect}
          onZoomIn={handleZoomIn}
          onZoomOut={handleZoomOut}
          onRotate={handleRotate}
          scale={state.scale}
        />
        
        <main className="flex-1 flex flex-col">
          <div className="flex items-center justify-center space-x-4 p-4 bg-gray-100 border-b">
            <button
              onClick={() => handlePageChange(state.currentPage - 1)}
              disabled={state.currentPage <= 1}
              className="p-2 rounded-lg bg-white border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft size={16} />
            </button>
            <span className="text-sm font-medium">
              Page {state.currentPage} of {state.numPages}
            </span>
            <button
              onClick={() => handlePageChange(state.currentPage + 1)}
              disabled={state.currentPage >= state.numPages}
              className="p-2 rounded-lg bg-white border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight size={16} />
            </button>
          </div>
          
          <PDFViewer
            file={state.file}
            currentPage={state.currentPage}
            scale={state.scale}
            rotation={state.rotation}
            activeTool={state.activeTool}
            onDocumentLoadSuccess={onDocumentLoadSuccess}
          />
        </main>
      </div>
    </div>
  );
};

export default PDFEditor;
