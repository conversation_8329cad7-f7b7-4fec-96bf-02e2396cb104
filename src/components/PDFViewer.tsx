'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Document, Page } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

interface PDFViewerProps {
  file: File;
  currentPage: number;
  scale: number;
  rotation: number;
  activeTool: string;
  onDocumentLoadSuccess: ({ numPages }: { numPages: number }) => void;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  file,
  currentPage,
  scale,
  rotation,
  activeTool,
  onDocumentLoadSuccess
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [pageWidth, setPageWidth] = useState(0);
  const [pageHeight, setPageHeight] = useState(0);

  const onPageLoadSuccess = (page: any) => {
    setPageWidth(page.width);
    setPageHeight(page.height);
  };

  return (
    <div className="flex-1 overflow-auto bg-gray-100">
      <div className="flex justify-center p-8">
        <div
          ref={containerRef}
          className="relative bg-white shadow-lg"
          style={{
            transform: `rotate(${rotation}deg)`,
            transformOrigin: 'center center'
          }}
        >
          <Document
            file={file}
            onLoadSuccess={onDocumentLoadSuccess}
            loading={
              <div className="flex items-center justify-center h-96">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            }
            error={
              <div className="flex items-center justify-center h-96 text-red-600">
                <p>Failed to load PDF. Please try again.</p>
              </div>
            }
          >
            <Page
              pageNumber={currentPage}
              scale={scale}
              onLoadSuccess={onPageLoadSuccess}
              loading={
                <div className="flex items-center justify-center h-96">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              }
            />
          </Document>

          {/* Placeholder for editing overlay - will add Fabric.js later */}
          <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
            {activeTool !== 'select' && (
              <div className="absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded text-sm">
                Active Tool: {activeTool}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFViewer;
