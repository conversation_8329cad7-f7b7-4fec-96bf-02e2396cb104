"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-pdf";
exports.ids = ["vendor-chunks/react-pdf"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css":
/*!******************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ae72b94089eb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2UvQW5ub3RhdGlvbkxheWVyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL3BkZmVkaXQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2UvQW5ub3RhdGlvbkxheWVyLmNzcz83NTI2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYWU3MmI5NDA4OWViXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.css":
/*!************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/TextLayer.css ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d2709c312083\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2UvVGV4dExheWVyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL3BkZmVkaXQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2UvVGV4dExheWVyLmNzcz84NDE5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDI3MDljMzEyMDgzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Document.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Document.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var make_event_props__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! make-event-props */ \"(ssr)/./node_modules/make-event-props/dist/esm/index.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var dequal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dequal */ \"(ssr)/./node_modules/dequal/dist/index.mjs\");\n/* harmony import */ var _pdfjs_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pdfjs.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/pdfjs.js\");\n/* harmony import */ var _DocumentContext_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./DocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js\");\n/* harmony import */ var _Message_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./Message.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Message.js\");\n/* harmony import */ var _LinkService_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./LinkService.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/LinkService.js\");\n/* harmony import */ var _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PasswordResponses.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/PasswordResponses.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./shared/propTypes.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/propTypes.js\");\n'use client';\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst { PDFDataRangeTransport } = _pdfjs_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst defaultOnPassword = (callback, reason) => {\n    switch (reason) {\n        case _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"].NEED_PASSWORD: {\n            // eslint-disable-next-line no-alert\n            const password = prompt('Enter the password to open this PDF file.');\n            callback(password);\n            break;\n        }\n        case _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"].INCORRECT_PASSWORD: {\n            // eslint-disable-next-line no-alert\n            const password = prompt('Invalid password. Please try again.');\n            callback(password);\n            break;\n        }\n        default:\n    }\n};\nfunction isParameterObject(file) {\n    return (typeof file === 'object' &&\n        file !== null &&\n        ('data' in file || 'range' in file || 'url' in file));\n}\n/**\n * Loads a document passed using `file` prop.\n */\nconst Document = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function Document(_a, ref) {\n    var { children, className, error = 'Failed to load PDF file.', externalLinkRel, externalLinkTarget, file, inputRef, imageResourcesPath, loading = 'Loading PDF…', noData = 'No PDF file specified.', onItemClick, onLoadError: onLoadErrorProps, onLoadProgress, onLoadSuccess: onLoadSuccessProps, onPassword = defaultOnPassword, onSourceError: onSourceErrorProps, onSourceSuccess: onSourceSuccessProps, options, renderMode, rotate } = _a, otherProps = __rest(_a, [\"children\", \"className\", \"error\", \"externalLinkRel\", \"externalLinkTarget\", \"file\", \"inputRef\", \"imageResourcesPath\", \"loading\", \"noData\", \"onItemClick\", \"onLoadError\", \"onLoadProgress\", \"onLoadSuccess\", \"onPassword\", \"onSourceError\", \"onSourceSuccess\", \"options\", \"renderMode\", \"rotate\"]);\n    const [sourceState, sourceDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const { value: source, error: sourceError } = sourceState;\n    const [pdfState, pdfDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const { value: pdf, error: pdfError } = pdfState;\n    const linkService = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new _LinkService_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]());\n    const pages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const prevFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const prevOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (file && file !== prevFile.current && isParameterObject(file)) {\n            warning__WEBPACK_IMPORTED_MODULE_3__(!(0,dequal__WEBPACK_IMPORTED_MODULE_4__.dequal)(file, prevFile.current), `File prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to \"file\" prop.`);\n            prevFile.current = file;\n        }\n    }, [file]);\n    // Detect non-memoized changes in options prop\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (options && options !== prevOptions.current) {\n            warning__WEBPACK_IMPORTED_MODULE_3__(!(0,dequal__WEBPACK_IMPORTED_MODULE_4__.dequal)(options, prevOptions.current), `Options prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to \"options\" prop.`);\n            prevOptions.current = options;\n        }\n    }, [options]);\n    const viewer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        // Handling jumping to internal links target\n        scrollPageIntoView: (args) => {\n            const { dest, pageNumber, pageIndex = pageNumber - 1 } = args;\n            // First, check if custom handling of onItemClick was provided\n            if (onItemClick) {\n                onItemClick({ dest, pageIndex, pageNumber });\n                return;\n            }\n            // If not, try to look for target page within the <Document>.\n            const page = pages.current[pageIndex];\n            if (page) {\n                // Scroll to the page automatically\n                page.scrollIntoView();\n                return;\n            }\n            warning__WEBPACK_IMPORTED_MODULE_3__(false, `An internal link leading to page ${pageNumber} was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>.`);\n        },\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => ({\n        linkService,\n        pages,\n        viewer,\n    }), []);\n    /**\n     * Called when a document source is resolved correctly\n     */\n    function onSourceSuccess() {\n        if (onSourceSuccessProps) {\n            onSourceSuccessProps();\n        }\n    }\n    /**\n     * Called when a document source failed to be resolved correctly\n     */\n    function onSourceError() {\n        if (!sourceError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, sourceError.toString());\n        if (onSourceErrorProps) {\n            onSourceErrorProps(sourceError);\n        }\n    }\n    function resetSource() {\n        sourceDispatch({ type: 'RESET' });\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(resetSource, [file, sourceDispatch]);\n    const findDocumentSource = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => __awaiter(this, void 0, void 0, function* () {\n        if (!file) {\n            return null;\n        }\n        // File is a string\n        if (typeof file === 'string') {\n            if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.isDataURI)(file)) {\n                const fileByteString = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.dataURItoByteString)(file);\n                return { data: fileByteString };\n            }\n            (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.displayCORSWarning)();\n            return { url: file };\n        }\n        // File is PDFDataRangeTransport\n        if (file instanceof PDFDataRangeTransport) {\n            return { range: file };\n        }\n        // File is an ArrayBuffer\n        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.isArrayBuffer)(file)) {\n            return { data: file };\n        }\n        /**\n         * The cases below are browser-only.\n         * If you're running on a non-browser environment, these cases will be of no use.\n         */\n        if (_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.isBrowser) {\n            // File is a Blob\n            if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.isBlob)(file)) {\n                const data = yield (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.loadFromFile)(file);\n                return { data };\n            }\n        }\n        // At this point, file must be an object\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(typeof file === 'object', 'Invalid parameter in file, need either Uint8Array, string or a parameter object');\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(isParameterObject(file), 'Invalid parameter object: need either .data, .range or .url');\n        // File .url is a string\n        if ('url' in file && typeof file.url === 'string') {\n            if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.isDataURI)(file.url)) {\n                const { url } = file, otherParams = __rest(file, [\"url\"]);\n                const fileByteString = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.dataURItoByteString)(url);\n                return Object.assign({ data: fileByteString }, otherParams);\n            }\n            (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.displayCORSWarning)();\n        }\n        return file;\n    }), [file]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(findDocumentSource());\n        cancellable.promise\n            .then((nextSource) => {\n            sourceDispatch({ type: 'RESOLVE', value: nextSource });\n        })\n            .catch((error) => {\n            sourceDispatch({ type: 'REJECT', error });\n        });\n        return () => {\n            (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.cancelRunningTask)(cancellable);\n        };\n    }, [findDocumentSource, sourceDispatch]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (typeof source === 'undefined') {\n            return;\n        }\n        if (source === false) {\n            onSourceError();\n            return;\n        }\n        onSourceSuccess();\n    }, \n    // Ommitted callbacks so they are not called every time they change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [source]);\n    /**\n     * Called when a document is read successfully\n     */\n    function onLoadSuccess() {\n        if (!pdf) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onLoadSuccessProps) {\n            onLoadSuccessProps(pdf);\n        }\n        pages.current = new Array(pdf.numPages);\n        linkService.current.setDocument(pdf);\n    }\n    /**\n     * Called when a document failed to read successfully\n     */\n    function onLoadError() {\n        if (!pdfError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, pdfError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(pdfError);\n        }\n    }\n    function resetDocument() {\n        pdfDispatch({ type: 'RESET' });\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(resetDocument, [pdfDispatch, source]);\n    function loadDocument() {\n        if (!source) {\n            return;\n        }\n        const optionsWithModifiedIsEvalSupported = Object.assign(Object.assign({}, options), { isEvalSupported: false });\n        const documentInitParams = Object.assign(Object.assign({}, source), optionsWithModifiedIsEvalSupported);\n        const destroyable = _pdfjs_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDocument(documentInitParams);\n        if (onLoadProgress) {\n            destroyable.onProgress = onLoadProgress;\n        }\n        if (onPassword) {\n            destroyable.onPassword = onPassword;\n        }\n        const loadingTask = destroyable;\n        loadingTask.promise\n            .then((nextPdf) => {\n            pdfDispatch({ type: 'RESOLVE', value: nextPdf });\n        })\n            .catch((error) => {\n            if (loadingTask.destroyed) {\n                return;\n            }\n            pdfDispatch({ type: 'REJECT', error });\n        });\n        return () => {\n            loadingTask.destroy();\n        };\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(loadDocument, \n    // Ommitted callbacks so they are not called every time they change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [options, pdfDispatch, source]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (typeof pdf === 'undefined') {\n            return;\n        }\n        if (pdf === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, \n    // Ommitted callbacks so they are not called every time they change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [pdf]);\n    function setupLinkService() {\n        linkService.current.setViewer(viewer.current);\n        linkService.current.setExternalLinkRel(externalLinkRel);\n        linkService.current.setExternalLinkTarget(externalLinkTarget);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(setupLinkService, [externalLinkRel, externalLinkTarget]);\n    function registerPage(pageIndex, ref) {\n        pages.current[pageIndex] = ref;\n    }\n    function unregisterPage(pageIndex) {\n        delete pages.current[pageIndex];\n    }\n    const childContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n        imageResourcesPath,\n        linkService: linkService.current,\n        onItemClick,\n        pdf,\n        registerPage,\n        renderMode,\n        rotate,\n        unregisterPage,\n    }), [imageResourcesPath, onItemClick, pdf, renderMode, rotate]);\n    const eventProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,make_event_props__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(otherProps, () => pdf), [otherProps, pdf]);\n    function renderChildren() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_DocumentContext_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Provider, { value: childContext }, children);\n    }\n    function renderContent() {\n        if (!file) {\n            return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Message_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"], { type: \"no-data\" }, typeof noData === 'function' ? noData() : noData);\n        }\n        if (pdf === undefined || pdf === null) {\n            return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Message_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"], { type: \"loading\" }, typeof loading === 'function' ? loading() : loading));\n        }\n        if (pdf === false) {\n            return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Message_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"], { type: \"error\" }, typeof error === 'function' ? error() : error);\n        }\n        return renderChildren();\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", Object.assign({ className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('react-pdf__Document', className), ref: inputRef, style: {\n            ['--scale-factor']: '1',\n        } }, eventProps), renderContent()));\n});\nconst isFunctionOrNode = prop_types__WEBPACK_IMPORTED_MODULE_14__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_14__.func, prop_types__WEBPACK_IMPORTED_MODULE_14__.node]);\nDocument.propTypes = Object.assign(Object.assign({}, _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_15__.eventProps), { children: prop_types__WEBPACK_IMPORTED_MODULE_14__.node, className: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_15__.isClassName, error: isFunctionOrNode, externalLinkRel: prop_types__WEBPACK_IMPORTED_MODULE_14__.string, externalLinkTarget: prop_types__WEBPACK_IMPORTED_MODULE_14__.oneOf(['_self', '_blank', '_parent', '_top']), file: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_15__.isFile, imageResourcesPath: prop_types__WEBPACK_IMPORTED_MODULE_14__.string, inputRef: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_15__.isRef, loading: isFunctionOrNode, noData: isFunctionOrNode, onItemClick: prop_types__WEBPACK_IMPORTED_MODULE_14__.func, onLoadError: prop_types__WEBPACK_IMPORTED_MODULE_14__.func, onLoadProgress: prop_types__WEBPACK_IMPORTED_MODULE_14__.func, onLoadSuccess: prop_types__WEBPACK_IMPORTED_MODULE_14__.func, onPassword: prop_types__WEBPACK_IMPORTED_MODULE_14__.func, onSourceError: prop_types__WEBPACK_IMPORTED_MODULE_14__.func, onSourceSuccess: prop_types__WEBPACK_IMPORTED_MODULE_14__.func, options: prop_types__WEBPACK_IMPORTED_MODULE_14__.shape({\n        canvasFactory: prop_types__WEBPACK_IMPORTED_MODULE_14__.any,\n        canvasMaxAreaInBytes: prop_types__WEBPACK_IMPORTED_MODULE_14__.number,\n        cMapPacked: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        CMapReaderFactory: prop_types__WEBPACK_IMPORTED_MODULE_14__.any,\n        cMapUrl: prop_types__WEBPACK_IMPORTED_MODULE_14__.string,\n        disableAutoFetch: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        disableFontFace: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        disableRange: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        disableStream: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        docBaseUrl: prop_types__WEBPACK_IMPORTED_MODULE_14__.string,\n        enableXfa: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        filterFactory: prop_types__WEBPACK_IMPORTED_MODULE_14__.any,\n        fontExtraProperties: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        httpHeaders: prop_types__WEBPACK_IMPORTED_MODULE_14__.object,\n        isEvalSupported: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        isOffscreenCanvasSupported: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        length: prop_types__WEBPACK_IMPORTED_MODULE_14__.number,\n        maxImageSize: prop_types__WEBPACK_IMPORTED_MODULE_14__.number,\n        ownerDocument: prop_types__WEBPACK_IMPORTED_MODULE_14__.any,\n        password: prop_types__WEBPACK_IMPORTED_MODULE_14__.string,\n        pdfBug: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        rangeChunkSize: prop_types__WEBPACK_IMPORTED_MODULE_14__.number,\n        StandardFontDataFactory: prop_types__WEBPACK_IMPORTED_MODULE_14__.any,\n        standardFontDataUrl: prop_types__WEBPACK_IMPORTED_MODULE_14__.string,\n        stopAtErrors: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        useSystemFonts: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        useWorkerFetch: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        verbosity: prop_types__WEBPACK_IMPORTED_MODULE_14__.number,\n        withCredentials: prop_types__WEBPACK_IMPORTED_MODULE_14__.bool,\n        worker: prop_types__WEBPACK_IMPORTED_MODULE_14__.any,\n    }), rotate: prop_types__WEBPACK_IMPORTED_MODULE_14__.number });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Document);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Document.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js":
/*!************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/DocumentContext.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n'use client';\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL0RvY3VtZW50Q29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ3NDO0FBQ3RDLGlFQUFlLG9EQUFhLE1BQU0sRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3BkZmVkaXQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL0RvY3VtZW50Q29udGV4dC5qcz9jZjdjIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVDb250ZXh0KG51bGwpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/LinkService.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/LinkService.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LinkService)\n/* harmony export */ });\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* Copyright 2015 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst DEFAULT_LINK_REL = 'noopener noreferrer nofollow';\nclass LinkService {\n    constructor() {\n        this.externalLinkEnabled = true;\n        this.externalLinkRel = undefined;\n        this.externalLinkTarget = undefined;\n        this.isInPresentationMode = false;\n        this.pdfDocument = undefined;\n        this.pdfViewer = undefined;\n    }\n    setDocument(pdfDocument) {\n        this.pdfDocument = pdfDocument;\n    }\n    setViewer(pdfViewer) {\n        this.pdfViewer = pdfViewer;\n    }\n    setExternalLinkRel(externalLinkRel) {\n        this.externalLinkRel = externalLinkRel;\n    }\n    setExternalLinkTarget(externalLinkTarget) {\n        this.externalLinkTarget = externalLinkTarget;\n    }\n    setHistory() {\n        // Intentionally empty\n    }\n    get pagesCount() {\n        return this.pdfDocument ? this.pdfDocument.numPages : 0;\n    }\n    get page() {\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        return this.pdfViewer.currentPageNumber || 0;\n    }\n    set page(value) {\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        this.pdfViewer.currentPageNumber = value;\n    }\n    // eslint-disable-next-line @typescript-eslint/class-literal-property-style\n    get rotation() {\n        return 0;\n    }\n    set rotation(value) {\n        // Intentionally empty\n    }\n    goToDestination(dest) {\n        return new Promise((resolve) => {\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfDocument, 'PDF document not loaded.');\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(dest, 'Destination is not specified.');\n            if (typeof dest === 'string') {\n                this.pdfDocument.getDestination(dest).then(resolve);\n            }\n            else if (Array.isArray(dest)) {\n                resolve(dest);\n            }\n            else {\n                dest.then(resolve);\n            }\n        }).then((explicitDest) => {\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Array.isArray(explicitDest), `\"${explicitDest}\" is not a valid destination array.`);\n            const destRef = explicitDest[0];\n            new Promise((resolve) => {\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfDocument, 'PDF document not loaded.');\n                if (destRef instanceof Object) {\n                    this.pdfDocument\n                        .getPageIndex(destRef)\n                        .then((pageIndex) => {\n                        resolve(pageIndex);\n                    })\n                        .catch(() => {\n                        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, `\"${destRef}\" is not a valid page reference.`);\n                    });\n                }\n                else if (typeof destRef === 'number') {\n                    resolve(destRef);\n                }\n                else {\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, `\"${destRef}\" is not a valid destination reference.`);\n                }\n            }).then((pageIndex) => {\n                const pageNumber = pageIndex + 1;\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(pageNumber >= 1 && pageNumber <= this.pagesCount, `\"${pageNumber}\" is not a valid page number.`);\n                this.pdfViewer.scrollPageIntoView({\n                    dest: explicitDest,\n                    pageIndex,\n                    pageNumber,\n                });\n            });\n        });\n    }\n    navigateTo(dest) {\n        this.goToDestination(dest);\n    }\n    goToPage(pageNumber) {\n        const pageIndex = pageNumber - 1;\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(pageNumber >= 1 && pageNumber <= this.pagesCount, `\"${pageNumber}\" is not a valid page number.`);\n        this.pdfViewer.scrollPageIntoView({\n            pageIndex,\n            pageNumber,\n        });\n    }\n    addLinkAttributes(link, url, newWindow) {\n        link.href = url;\n        link.rel = this.externalLinkRel || DEFAULT_LINK_REL;\n        link.target = newWindow ? '_blank' : this.externalLinkTarget || '';\n    }\n    getDestinationHash() {\n        return '#';\n    }\n    getAnchorUrl() {\n        return '#';\n    }\n    setHash() {\n        // Intentionally empty\n    }\n    executeNamedAction() {\n        // Intentionally empty\n    }\n    cachePageRef() {\n        // Intentionally empty\n    }\n    isPageVisible() {\n        return true;\n    }\n    isPageCached() {\n        return true;\n    }\n    executeSetOCGState() {\n        // Intentionally empty\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/LinkService.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Message.js":
/*!****************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Message.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Message)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction Message({ children, type }) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: `react-pdf__message react-pdf__message--${type}` }, children);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL01lc3NhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDWCxtQkFBbUIsZ0JBQWdCO0FBQ2xELFdBQVcsZ0RBQW1CLFVBQVUscURBQXFELEtBQUssR0FBRztBQUNyRyIsInNvdXJjZXMiOlsid2VicGFjazovL3BkZmVkaXQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL01lc3NhZ2UuanM/ODViMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWVzc2FnZSh7IGNoaWxkcmVuLCB0eXBlIH0pIHtcbiAgICByZXR1cm4gUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7IGNsYXNzTmFtZTogYHJlYWN0LXBkZl9fbWVzc2FnZSByZWFjdC1wZGZfX21lc3NhZ2UtLSR7dHlwZX1gIH0sIGNoaWxkcmVuKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Message.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page.js":
/*!*************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var make_event_props__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! make-event-props */ \"(ssr)/./node_modules/make-event-props/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var merge_refs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! merge-refs */ \"(ssr)/./node_modules/merge-refs/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var _PageContext_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js\");\n/* harmony import */ var _Message_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Message.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Message.js\");\n/* harmony import */ var _Page_PageCanvas_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Page/PageCanvas.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/PageCanvas.js\");\n/* harmony import */ var _Page_PageSVG_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Page/PageSVG.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/PageSVG.js\");\n/* harmony import */ var _Page_TextLayer_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./Page/TextLayer.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js\");\n/* harmony import */ var _Page_AnnotationLayer_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Page/AnnotationLayer.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./shared/hooks/useDocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./shared/propTypes.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/propTypes.js\");\n'use client';\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst defaultScale = 1;\n/**\n * Displays a page.\n *\n * Should be placed inside `<Document />`. Alternatively, it can have `pdf` prop passed, which can be obtained from `<Document />`'s `onLoadSuccess` callback function, however some advanced functions like linking between pages inside a document may not be working correctly.\n */\nconst Page = function Page(props) {\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const mergedProps = Object.assign(Object.assign({}, documentContext), props);\n    const { _className = 'react-pdf__Page', _enableRegisterUnregisterPage = true, canvasBackground, canvasRef, children, className, customRenderer: CustomRenderer, customTextRenderer, devicePixelRatio, error = 'Failed to load the page.', height, inputRef, loading = 'Loading page…', noData = 'No page specified.', onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, onGetTextError: onGetTextErrorProps, onGetTextSuccess: onGetTextSuccessProps, onLoadError: onLoadErrorProps, onLoadSuccess: onLoadSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, onRenderTextLayerError: onRenderTextLayerErrorProps, onRenderTextLayerSuccess: onRenderTextLayerSuccessProps, pageIndex: pageIndexProps, pageNumber: pageNumberProps, pdf, registerPage, renderAnnotationLayer: renderAnnotationLayerProps = true, renderForms = false, renderMode = 'canvas', renderTextLayer: renderTextLayerProps = true, rotate: rotateProps, scale: scaleProps = defaultScale, unregisterPage, width } = mergedProps, otherProps = __rest(mergedProps, [\"_className\", \"_enableRegisterUnregisterPage\", \"canvasBackground\", \"canvasRef\", \"children\", \"className\", \"customRenderer\", \"customTextRenderer\", \"devicePixelRatio\", \"error\", \"height\", \"inputRef\", \"loading\", \"noData\", \"onGetAnnotationsError\", \"onGetAnnotationsSuccess\", \"onGetStructTreeError\", \"onGetStructTreeSuccess\", \"onGetTextError\", \"onGetTextSuccess\", \"onLoadError\", \"onLoadSuccess\", \"onRenderAnnotationLayerError\", \"onRenderAnnotationLayerSuccess\", \"onRenderError\", \"onRenderSuccess\", \"onRenderTextLayerError\", \"onRenderTextLayerSuccess\", \"pageIndex\", \"pageNumber\", \"pdf\", \"registerPage\", \"renderAnnotationLayer\", \"renderForms\", \"renderMode\", \"renderTextLayer\", \"rotate\", \"scale\", \"unregisterPage\", \"width\"]);\n    const [pageState, pageDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { value: page, error: pageError } = pageState;\n    const pageElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pdf, 'Attempted to load a page, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.');\n    const pageIndex = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.isProvided)(pageNumberProps) ? pageNumberProps - 1 : pageIndexProps !== null && pageIndexProps !== void 0 ? pageIndexProps : null;\n    const pageNumber = pageNumberProps !== null && pageNumberProps !== void 0 ? pageNumberProps : ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.isProvided)(pageIndexProps) ? pageIndexProps + 1 : null);\n    const rotate = rotateProps !== null && rotateProps !== void 0 ? rotateProps : (page ? page.rotate : null);\n    const scale = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        if (!page) {\n            return null;\n        }\n        // Be default, we'll render page at 100% * scale width.\n        let pageScale = 1;\n        // Passing scale explicitly null would cause the page not to render\n        const scaleWithDefault = scaleProps !== null && scaleProps !== void 0 ? scaleProps : defaultScale;\n        // If width/height is defined, calculate the scale of the page so it could be of desired width.\n        if (width || height) {\n            const viewport = page.getViewport({ scale: 1, rotation: rotate });\n            if (width) {\n                pageScale = width / viewport.width;\n            }\n            else if (height) {\n                pageScale = height / viewport.height;\n            }\n        }\n        return scaleWithDefault * pageScale;\n    }, [height, page, rotate, scaleProps, width]);\n    function hook() {\n        return () => {\n            if (!(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.isProvided)(pageIndex)) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            if (_enableRegisterUnregisterPage && unregisterPage) {\n                unregisterPage(pageIndex);\n            }\n        };\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(hook, [_enableRegisterUnregisterPage, pdf, pageIndex, unregisterPage]);\n    /**\n     * Called when a page is loaded successfully\n     */\n    function onLoadSuccess() {\n        if (onLoadSuccessProps) {\n            if (!page || !scale) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            onLoadSuccessProps((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.makePageCallback)(page, scale));\n        }\n        if (_enableRegisterUnregisterPage && registerPage) {\n            if (!(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.isProvided)(pageIndex) || !pageElement.current) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            registerPage(pageIndex, pageElement.current);\n        }\n    }\n    /**\n     * Called when a page failed to load\n     */\n    function onLoadError() {\n        if (!pageError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, pageError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(pageError);\n        }\n    }\n    function resetPage() {\n        pageDispatch({ type: 'RESET' });\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(resetPage, [pageDispatch, pdf, pageIndex]);\n    function loadPage() {\n        if (!pdf || !pageNumber) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(pdf.getPage(pageNumber));\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextPage) => {\n            pageDispatch({ type: 'RESOLVE', value: nextPage });\n        })\n            .catch((error) => {\n            pageDispatch({ type: 'REJECT', error });\n        });\n        return () => (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.cancelRunningTask)(runningTask);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(loadPage, [pageDispatch, pdf, pageIndex, pageNumber, registerPage]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (page === undefined) {\n            return;\n        }\n        if (page === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, \n    // Ommitted callbacks so they are not called every time they change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [page, scale]);\n    const childContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => \n    // Technically there cannot be page without pageIndex, pageNumber, rotate and scale, but TypeScript doesn't know that\n    page && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.isProvided)(pageIndex) && pageNumber && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.isProvided)(rotate) && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.isProvided)(scale)\n        ? {\n            _className,\n            canvasBackground,\n            customTextRenderer,\n            devicePixelRatio,\n            onGetAnnotationsError: onGetAnnotationsErrorProps,\n            onGetAnnotationsSuccess: onGetAnnotationsSuccessProps,\n            onGetStructTreeError: onGetStructTreeErrorProps,\n            onGetStructTreeSuccess: onGetStructTreeSuccessProps,\n            onGetTextError: onGetTextErrorProps,\n            onGetTextSuccess: onGetTextSuccessProps,\n            onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps,\n            onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps,\n            onRenderError: onRenderErrorProps,\n            onRenderSuccess: onRenderSuccessProps,\n            onRenderTextLayerError: onRenderTextLayerErrorProps,\n            onRenderTextLayerSuccess: onRenderTextLayerSuccessProps,\n            page,\n            pageIndex,\n            pageNumber,\n            renderForms,\n            renderTextLayer: renderTextLayerProps,\n            rotate,\n            scale,\n        }\n        : null, [\n        _className,\n        canvasBackground,\n        customTextRenderer,\n        devicePixelRatio,\n        onGetAnnotationsErrorProps,\n        onGetAnnotationsSuccessProps,\n        onGetStructTreeErrorProps,\n        onGetStructTreeSuccessProps,\n        onGetTextErrorProps,\n        onGetTextSuccessProps,\n        onRenderAnnotationLayerErrorProps,\n        onRenderAnnotationLayerSuccessProps,\n        onRenderErrorProps,\n        onRenderSuccessProps,\n        onRenderTextLayerErrorProps,\n        onRenderTextLayerSuccessProps,\n        page,\n        pageIndex,\n        pageNumber,\n        renderForms,\n        renderTextLayerProps,\n        rotate,\n        scale,\n    ]);\n    const eventProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,make_event_props__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(otherProps, () => page ? (scale ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.makePageCallback)(page, scale) : undefined) : page), [otherProps, page, scale]);\n    const pageKey = `${pageIndex}@${scale}/${rotate}`;\n    const pageKeyNoScale = `${pageIndex}/${rotate}`;\n    function renderMainLayer() {\n        switch (renderMode) {\n            case 'custom': {\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(CustomRenderer, `renderMode was set to \"custom\", but no customRenderer was passed.`);\n                return react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomRenderer, { key: `${pageKey}_custom` });\n            }\n            case 'none':\n                return null;\n            case 'svg':\n                return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Page_PageSVG_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"], { key: `${pageKeyNoScale}_svg` });\n            case 'canvas':\n            default:\n                return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Page_PageCanvas_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"], { key: `${pageKey}_canvas`, canvasRef: canvasRef });\n        }\n    }\n    function renderTextLayer() {\n        if (!renderTextLayerProps) {\n            return null;\n        }\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Page_TextLayer_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"], { key: `${pageKey}_text` });\n    }\n    function renderAnnotationLayer() {\n        if (!renderAnnotationLayerProps) {\n            return null;\n        }\n        /**\n         * As of now, PDF.js 2.0.943 returns warnings on unimplemented annotations in SVG mode.\n         * Therefore, as a fallback, we render \"traditional\" AnnotationLayer component.\n         */\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Page_AnnotationLayer_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"], { key: `${pageKey}_annotations` });\n    }\n    function renderChildren() {\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PageContext_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"].Provider, { value: childContext },\n            renderMainLayer(),\n            renderTextLayer(),\n            renderAnnotationLayer(),\n            children));\n    }\n    function renderContent() {\n        if (!pageNumber) {\n            return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], { type: \"no-data\" }, typeof noData === 'function' ? noData() : noData);\n        }\n        if (pdf === null || page === undefined || page === null) {\n            return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], { type: \"loading\" }, typeof loading === 'function' ? loading() : loading));\n        }\n        if (pdf === false || page === false) {\n            return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], { type: \"error\" }, typeof error === 'function' ? error() : error);\n        }\n        return renderChildren();\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", Object.assign({ className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_className, className), \"data-page-number\": pageNumber, ref: (0,merge_refs__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(inputRef, pageElement), style: {\n            ['--scale-factor']: `${scale}`,\n            backgroundColor: canvasBackground || 'white',\n            position: 'relative',\n            minWidth: 'min-content',\n            minHeight: 'min-content',\n        } }, eventProps), renderContent()));\n};\nconst isFunctionOrNode = prop_types__WEBPACK_IMPORTED_MODULE_16__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_16__.func, prop_types__WEBPACK_IMPORTED_MODULE_16__.node]);\nPage.propTypes = Object.assign(Object.assign({}, _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_17__.eventProps), { canvasBackground: prop_types__WEBPACK_IMPORTED_MODULE_16__.string, canvasRef: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_17__.isRef, children: prop_types__WEBPACK_IMPORTED_MODULE_16__.node, className: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_17__.isClassName, customRenderer: prop_types__WEBPACK_IMPORTED_MODULE_16__.func, customTextRenderer: prop_types__WEBPACK_IMPORTED_MODULE_16__.func, devicePixelRatio: prop_types__WEBPACK_IMPORTED_MODULE_16__.number, error: isFunctionOrNode, height: prop_types__WEBPACK_IMPORTED_MODULE_16__.number, imageResourcesPath: prop_types__WEBPACK_IMPORTED_MODULE_16__.string, inputRef: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_17__.isRef, loading: isFunctionOrNode, noData: isFunctionOrNode, onGetTextError: prop_types__WEBPACK_IMPORTED_MODULE_16__.func, onGetTextSuccess: prop_types__WEBPACK_IMPORTED_MODULE_16__.func, onLoadError: prop_types__WEBPACK_IMPORTED_MODULE_16__.func, onLoadSuccess: prop_types__WEBPACK_IMPORTED_MODULE_16__.func, onRenderError: prop_types__WEBPACK_IMPORTED_MODULE_16__.func, onRenderSuccess: prop_types__WEBPACK_IMPORTED_MODULE_16__.func, onRenderTextLayerError: prop_types__WEBPACK_IMPORTED_MODULE_16__.func, onRenderTextLayerSuccess: prop_types__WEBPACK_IMPORTED_MODULE_16__.func, pageIndex: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_17__.isPageIndex, pageNumber: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_17__.isPageNumber, pdf: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_17__.isPdf, renderAnnotationLayer: prop_types__WEBPACK_IMPORTED_MODULE_16__.bool, renderForms: prop_types__WEBPACK_IMPORTED_MODULE_16__.bool, renderMode: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_17__.isRenderMode, renderTextLayer: prop_types__WEBPACK_IMPORTED_MODULE_16__.bool, rotate: _shared_propTypes_js__WEBPACK_IMPORTED_MODULE_17__.isRotate, scale: prop_types__WEBPACK_IMPORTED_MODULE_16__.number, width: prop_types__WEBPACK_IMPORTED_MODULE_16__.number });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Page);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnnotationLayer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var _pdfjs_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pdfjs.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/pdfjs.js\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../shared/hooks/useDocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n'use client';\n\n\n\n\n\n\n\n\n\n\nfunction AnnotationLayer() {\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const mergedProps = Object.assign(Object.assign({}, documentContext), pageContext);\n    const { imageResourcesPath, linkService, onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, page, pdf, renderForms, rotate, scale = 1, } = mergedProps;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pdf, 'Attempted to load page annotations, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.');\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(page, 'Attempted to load page annotations, but no page was specified.');\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(linkService, 'Attempted to load page annotations, but no linkService was specified.');\n    const [annotationsState, annotationsDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const { value: annotations, error: annotationsError } = annotationsState;\n    const layerElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    warning__WEBPACK_IMPORTED_MODULE_3__(parseInt(window.getComputedStyle(document.body).getPropertyValue('--react-pdf-annotation-layer'), 10) === 1, 'AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations');\n    function onLoadSuccess() {\n        if (!annotations) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetAnnotationsSuccessProps) {\n            onGetAnnotationsSuccessProps(annotations);\n        }\n    }\n    function onLoadError() {\n        if (!annotationsError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, annotationsError.toString());\n        if (onGetAnnotationsErrorProps) {\n            onGetAnnotationsErrorProps(annotationsError);\n        }\n    }\n    function resetAnnotations() {\n        annotationsDispatch({ type: 'RESET' });\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(resetAnnotations, [annotationsDispatch, page]);\n    function loadAnnotations() {\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(page.getAnnotations());\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextAnnotations) => {\n            annotationsDispatch({ type: 'RESOLVE', value: nextAnnotations });\n        })\n            .catch((error) => {\n            annotationsDispatch({ type: 'REJECT', error });\n        });\n        return () => {\n            (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_8__.cancelRunningTask)(runningTask);\n        };\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(loadAnnotations, [annotationsDispatch, page, renderForms]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (annotations === undefined) {\n            return;\n        }\n        if (annotations === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, \n    // Ommitted callbacks so they are not called every time they change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [annotations]);\n    function onRenderSuccess() {\n        if (onRenderAnnotationLayerSuccessProps) {\n            onRenderAnnotationLayerSuccessProps();\n        }\n    }\n    function onRenderError(error) {\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, `${error}`);\n        if (onRenderAnnotationLayerErrorProps) {\n            onRenderAnnotationLayerErrorProps(error);\n        }\n    }\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => page.getViewport({ scale, rotation: rotate }), [page, rotate, scale]);\n    function renderAnnotationLayer() {\n        if (!pdf || !page || !linkService || !annotations) {\n            return;\n        }\n        const { current: layer } = layerElement;\n        if (!layer) {\n            return;\n        }\n        const clonedViewport = viewport.clone({ dontFlip: true });\n        const annotationLayerParameters = {\n            accessibilityManager: null, // TODO: Implement this\n            annotationCanvasMap: null, // TODO: Implement this\n            div: layer,\n            l10n: null, // TODO: Implement this\n            page,\n            viewport: clonedViewport,\n        };\n        const renderParameters = {\n            annotations,\n            annotationStorage: pdf.annotationStorage,\n            div: layer,\n            // See https://github.com/mozilla/pdf.js/issues/17029\n            downloadManager: null,\n            imageResourcesPath,\n            linkService,\n            page,\n            renderForms,\n            viewport: clonedViewport,\n        };\n        layer.innerHTML = '';\n        try {\n            new _pdfjs_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"].AnnotationLayer(annotationLayerParameters).render(renderParameters);\n            // Intentional immediate callback\n            onRenderSuccess();\n        }\n        catch (error) {\n            onRenderError(error);\n        }\n        return () => {\n            // TODO: Cancel running task?\n        };\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(renderAnnotationLayer, \n    // Ommitted callbacks so they are not called every time they change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [annotations, imageResourcesPath, linkService, page, renderForms, viewport]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('react-pdf__Page__annotations', 'annotationLayer'), ref: layerElement }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2UvQW5ub3RhdGlvbkxheWVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDMEQ7QUFDSDtBQUMvQjtBQUNlO0FBQ1Q7QUFDRTtBQUN1QztBQUNSO0FBQ047QUFDRjtBQUN4QztBQUNmLDRCQUE0QiwrRUFBa0I7QUFDOUMsd0JBQXdCLDJFQUFjO0FBQ3RDLElBQUksMERBQVM7QUFDYixzREFBc0Q7QUFDdEQsWUFBWSw4VEFBOFQ7QUFDMVUsSUFBSSwwREFBUztBQUNiLElBQUksMERBQVM7QUFDYixJQUFJLDBEQUFTO0FBQ2Isb0RBQW9ELHdFQUFXO0FBQy9ELFlBQVksOENBQThDO0FBQzFELHlCQUF5Qiw2Q0FBTTtBQUMvQixJQUFJLG9DQUFPO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsb0NBQU87QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLGVBQWU7QUFDN0M7QUFDQSxJQUFJLGdEQUFTO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsb0VBQWU7QUFDM0M7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLHlDQUF5QztBQUMzRSxTQUFTO0FBQ1Q7QUFDQSxrQ0FBa0MsdUJBQXVCO0FBQ3pELFNBQVM7QUFDVDtBQUNBLFlBQVksbUVBQWlCO0FBQzdCO0FBQ0E7QUFDQSxJQUFJLGdEQUFTO0FBQ2IsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxvQ0FBTyxXQUFXLE1BQU07QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsOENBQU8sMEJBQTBCLHlCQUF5QjtBQUMvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixpQkFBaUI7QUFDakM7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELGdCQUFnQjtBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixpREFBSztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQSxZQUFZLGdEQUFtQixVQUFVLFdBQVcsZ0RBQUksd0VBQXdFO0FBQ2hJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGRmZWRpdC8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vUGFnZS9Bbm5vdGF0aW9uTGF5ZXIuanM/OTRjNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VNZW1vLCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgbWFrZUNhbmNlbGxhYmxlIGZyb20gJ21ha2UtY2FuY2VsbGFibGUtcHJvbWlzZSc7XG5pbXBvcnQgY2xzeCBmcm9tICdjbHN4JztcbmltcG9ydCBpbnZhcmlhbnQgZnJvbSAndGlueS1pbnZhcmlhbnQnO1xuaW1wb3J0IHdhcm5pbmcgZnJvbSAnd2FybmluZyc7XG5pbXBvcnQgcGRmanMgZnJvbSAnLi4vcGRmanMuanMnO1xuaW1wb3J0IHVzZURvY3VtZW50Q29udGV4dCBmcm9tICcuLi9zaGFyZWQvaG9va3MvdXNlRG9jdW1lbnRDb250ZXh0LmpzJztcbmltcG9ydCB1c2VQYWdlQ29udGV4dCBmcm9tICcuLi9zaGFyZWQvaG9va3MvdXNlUGFnZUNvbnRleHQuanMnO1xuaW1wb3J0IHVzZVJlc29sdmVyIGZyb20gJy4uL3NoYXJlZC9ob29rcy91c2VSZXNvbHZlci5qcyc7XG5pbXBvcnQgeyBjYW5jZWxSdW5uaW5nVGFzayB9IGZyb20gJy4uL3NoYXJlZC91dGlscy5qcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBbm5vdGF0aW9uTGF5ZXIoKSB7XG4gICAgY29uc3QgZG9jdW1lbnRDb250ZXh0ID0gdXNlRG9jdW1lbnRDb250ZXh0KCk7XG4gICAgY29uc3QgcGFnZUNvbnRleHQgPSB1c2VQYWdlQ29udGV4dCgpO1xuICAgIGludmFyaWFudChwYWdlQ29udGV4dCwgJ1VuYWJsZSB0byBmaW5kIFBhZ2UgY29udGV4dC4nKTtcbiAgICBjb25zdCBtZXJnZWRQcm9wcyA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgZG9jdW1lbnRDb250ZXh0KSwgcGFnZUNvbnRleHQpO1xuICAgIGNvbnN0IHsgaW1hZ2VSZXNvdXJjZXNQYXRoLCBsaW5rU2VydmljZSwgb25HZXRBbm5vdGF0aW9uc0Vycm9yOiBvbkdldEFubm90YXRpb25zRXJyb3JQcm9wcywgb25HZXRBbm5vdGF0aW9uc1N1Y2Nlc3M6IG9uR2V0QW5ub3RhdGlvbnNTdWNjZXNzUHJvcHMsIG9uUmVuZGVyQW5ub3RhdGlvbkxheWVyRXJyb3I6IG9uUmVuZGVyQW5ub3RhdGlvbkxheWVyRXJyb3JQcm9wcywgb25SZW5kZXJBbm5vdGF0aW9uTGF5ZXJTdWNjZXNzOiBvblJlbmRlckFubm90YXRpb25MYXllclN1Y2Nlc3NQcm9wcywgcGFnZSwgcGRmLCByZW5kZXJGb3Jtcywgcm90YXRlLCBzY2FsZSA9IDEsIH0gPSBtZXJnZWRQcm9wcztcbiAgICBpbnZhcmlhbnQocGRmLCAnQXR0ZW1wdGVkIHRvIGxvYWQgcGFnZSBhbm5vdGF0aW9ucywgYnV0IG5vIGRvY3VtZW50IHdhcyBzcGVjaWZpZWQuIFdyYXAgPFBhZ2UgLz4gaW4gYSA8RG9jdW1lbnQgLz4gb3IgcGFzcyBleHBsaWNpdCBgcGRmYCBwcm9wLicpO1xuICAgIGludmFyaWFudChwYWdlLCAnQXR0ZW1wdGVkIHRvIGxvYWQgcGFnZSBhbm5vdGF0aW9ucywgYnV0IG5vIHBhZ2Ugd2FzIHNwZWNpZmllZC4nKTtcbiAgICBpbnZhcmlhbnQobGlua1NlcnZpY2UsICdBdHRlbXB0ZWQgdG8gbG9hZCBwYWdlIGFubm90YXRpb25zLCBidXQgbm8gbGlua1NlcnZpY2Ugd2FzIHNwZWNpZmllZC4nKTtcbiAgICBjb25zdCBbYW5ub3RhdGlvbnNTdGF0ZSwgYW5ub3RhdGlvbnNEaXNwYXRjaF0gPSB1c2VSZXNvbHZlcigpO1xuICAgIGNvbnN0IHsgdmFsdWU6IGFubm90YXRpb25zLCBlcnJvcjogYW5ub3RhdGlvbnNFcnJvciB9ID0gYW5ub3RhdGlvbnNTdGF0ZTtcbiAgICBjb25zdCBsYXllckVsZW1lbnQgPSB1c2VSZWYobnVsbCk7XG4gICAgd2FybmluZyhwYXJzZUludCh3aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZShkb2N1bWVudC5ib2R5KS5nZXRQcm9wZXJ0eVZhbHVlKCctLXJlYWN0LXBkZi1hbm5vdGF0aW9uLWxheWVyJyksIDEwKSA9PT0gMSwgJ0Fubm90YXRpb25MYXllciBzdHlsZXMgbm90IGZvdW5kLiBSZWFkIG1vcmU6IGh0dHBzOi8vZ2l0aHViLmNvbS93b2p0ZWttYWovcmVhY3QtcGRmI3N1cHBvcnQtZm9yLWFubm90YXRpb25zJyk7XG4gICAgZnVuY3Rpb24gb25Mb2FkU3VjY2VzcygpIHtcbiAgICAgICAgaWYgKCFhbm5vdGF0aW9ucykge1xuICAgICAgICAgICAgLy8gSW1wb3NzaWJsZSwgYnV0IFR5cGVTY3JpcHQgZG9lc24ndCBrbm93IHRoYXRcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAob25HZXRBbm5vdGF0aW9uc1N1Y2Nlc3NQcm9wcykge1xuICAgICAgICAgICAgb25HZXRBbm5vdGF0aW9uc1N1Y2Nlc3NQcm9wcyhhbm5vdGF0aW9ucyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gb25Mb2FkRXJyb3IoKSB7XG4gICAgICAgIGlmICghYW5ub3RhdGlvbnNFcnJvcikge1xuICAgICAgICAgICAgLy8gSW1wb3NzaWJsZSwgYnV0IFR5cGVTY3JpcHQgZG9lc24ndCBrbm93IHRoYXRcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB3YXJuaW5nKGZhbHNlLCBhbm5vdGF0aW9uc0Vycm9yLnRvU3RyaW5nKCkpO1xuICAgICAgICBpZiAob25HZXRBbm5vdGF0aW9uc0Vycm9yUHJvcHMpIHtcbiAgICAgICAgICAgIG9uR2V0QW5ub3RhdGlvbnNFcnJvclByb3BzKGFubm90YXRpb25zRXJyb3IpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGZ1bmN0aW9uIHJlc2V0QW5ub3RhdGlvbnMoKSB7XG4gICAgICAgIGFubm90YXRpb25zRGlzcGF0Y2goeyB0eXBlOiAnUkVTRVQnIH0pO1xuICAgIH1cbiAgICB1c2VFZmZlY3QocmVzZXRBbm5vdGF0aW9ucywgW2Fubm90YXRpb25zRGlzcGF0Y2gsIHBhZ2VdKTtcbiAgICBmdW5jdGlvbiBsb2FkQW5ub3RhdGlvbnMoKSB7XG4gICAgICAgIGlmICghcGFnZSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGNhbmNlbGxhYmxlID0gbWFrZUNhbmNlbGxhYmxlKHBhZ2UuZ2V0QW5ub3RhdGlvbnMoKSk7XG4gICAgICAgIGNvbnN0IHJ1bm5pbmdUYXNrID0gY2FuY2VsbGFibGU7XG4gICAgICAgIGNhbmNlbGxhYmxlLnByb21pc2VcbiAgICAgICAgICAgIC50aGVuKChuZXh0QW5ub3RhdGlvbnMpID0+IHtcbiAgICAgICAgICAgIGFubm90YXRpb25zRGlzcGF0Y2goeyB0eXBlOiAnUkVTT0xWRScsIHZhbHVlOiBuZXh0QW5ub3RhdGlvbnMgfSk7XG4gICAgICAgIH0pXG4gICAgICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICAgICAgICBhbm5vdGF0aW9uc0Rpc3BhdGNoKHsgdHlwZTogJ1JFSkVDVCcsIGVycm9yIH0pO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIGNhbmNlbFJ1bm5pbmdUYXNrKHJ1bm5pbmdUYXNrKTtcbiAgICAgICAgfTtcbiAgICB9XG4gICAgdXNlRWZmZWN0KGxvYWRBbm5vdGF0aW9ucywgW2Fubm90YXRpb25zRGlzcGF0Y2gsIHBhZ2UsIHJlbmRlckZvcm1zXSk7XG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKGFubm90YXRpb25zID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoYW5ub3RhdGlvbnMgPT09IGZhbHNlKSB7XG4gICAgICAgICAgICBvbkxvYWRFcnJvcigpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIG9uTG9hZFN1Y2Nlc3MoKTtcbiAgICB9LCBcbiAgICAvLyBPbW1pdHRlZCBjYWxsYmFja3Mgc28gdGhleSBhcmUgbm90IGNhbGxlZCBldmVyeSB0aW1lIHRoZXkgY2hhbmdlXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICAgIFthbm5vdGF0aW9uc10pO1xuICAgIGZ1bmN0aW9uIG9uUmVuZGVyU3VjY2VzcygpIHtcbiAgICAgICAgaWYgKG9uUmVuZGVyQW5ub3RhdGlvbkxheWVyU3VjY2Vzc1Byb3BzKSB7XG4gICAgICAgICAgICBvblJlbmRlckFubm90YXRpb25MYXllclN1Y2Nlc3NQcm9wcygpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGZ1bmN0aW9uIG9uUmVuZGVyRXJyb3IoZXJyb3IpIHtcbiAgICAgICAgd2FybmluZyhmYWxzZSwgYCR7ZXJyb3J9YCk7XG4gICAgICAgIGlmIChvblJlbmRlckFubm90YXRpb25MYXllckVycm9yUHJvcHMpIHtcbiAgICAgICAgICAgIG9uUmVuZGVyQW5ub3RhdGlvbkxheWVyRXJyb3JQcm9wcyhlcnJvcik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29uc3Qgdmlld3BvcnQgPSB1c2VNZW1vKCgpID0+IHBhZ2UuZ2V0Vmlld3BvcnQoeyBzY2FsZSwgcm90YXRpb246IHJvdGF0ZSB9KSwgW3BhZ2UsIHJvdGF0ZSwgc2NhbGVdKTtcbiAgICBmdW5jdGlvbiByZW5kZXJBbm5vdGF0aW9uTGF5ZXIoKSB7XG4gICAgICAgIGlmICghcGRmIHx8ICFwYWdlIHx8ICFsaW5rU2VydmljZSB8fCAhYW5ub3RhdGlvbnMpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB7IGN1cnJlbnQ6IGxheWVyIH0gPSBsYXllckVsZW1lbnQ7XG4gICAgICAgIGlmICghbGF5ZXIpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBjbG9uZWRWaWV3cG9ydCA9IHZpZXdwb3J0LmNsb25lKHsgZG9udEZsaXA6IHRydWUgfSk7XG4gICAgICAgIGNvbnN0IGFubm90YXRpb25MYXllclBhcmFtZXRlcnMgPSB7XG4gICAgICAgICAgICBhY2Nlc3NpYmlsaXR5TWFuYWdlcjogbnVsbCwgLy8gVE9ETzogSW1wbGVtZW50IHRoaXNcbiAgICAgICAgICAgIGFubm90YXRpb25DYW52YXNNYXA6IG51bGwsIC8vIFRPRE86IEltcGxlbWVudCB0aGlzXG4gICAgICAgICAgICBkaXY6IGxheWVyLFxuICAgICAgICAgICAgbDEwbjogbnVsbCwgLy8gVE9ETzogSW1wbGVtZW50IHRoaXNcbiAgICAgICAgICAgIHBhZ2UsXG4gICAgICAgICAgICB2aWV3cG9ydDogY2xvbmVkVmlld3BvcnQsXG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHJlbmRlclBhcmFtZXRlcnMgPSB7XG4gICAgICAgICAgICBhbm5vdGF0aW9ucyxcbiAgICAgICAgICAgIGFubm90YXRpb25TdG9yYWdlOiBwZGYuYW5ub3RhdGlvblN0b3JhZ2UsXG4gICAgICAgICAgICBkaXY6IGxheWVyLFxuICAgICAgICAgICAgLy8gU2VlIGh0dHBzOi8vZ2l0aHViLmNvbS9tb3ppbGxhL3BkZi5qcy9pc3N1ZXMvMTcwMjlcbiAgICAgICAgICAgIGRvd25sb2FkTWFuYWdlcjogbnVsbCxcbiAgICAgICAgICAgIGltYWdlUmVzb3VyY2VzUGF0aCxcbiAgICAgICAgICAgIGxpbmtTZXJ2aWNlLFxuICAgICAgICAgICAgcGFnZSxcbiAgICAgICAgICAgIHJlbmRlckZvcm1zLFxuICAgICAgICAgICAgdmlld3BvcnQ6IGNsb25lZFZpZXdwb3J0LFxuICAgICAgICB9O1xuICAgICAgICBsYXllci5pbm5lckhUTUwgPSAnJztcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIG5ldyBwZGZqcy5Bbm5vdGF0aW9uTGF5ZXIoYW5ub3RhdGlvbkxheWVyUGFyYW1ldGVycykucmVuZGVyKHJlbmRlclBhcmFtZXRlcnMpO1xuICAgICAgICAgICAgLy8gSW50ZW50aW9uYWwgaW1tZWRpYXRlIGNhbGxiYWNrXG4gICAgICAgICAgICBvblJlbmRlclN1Y2Nlc3MoKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIG9uUmVuZGVyRXJyb3IoZXJyb3IpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICAvLyBUT0RPOiBDYW5jZWwgcnVubmluZyB0YXNrP1xuICAgICAgICB9O1xuICAgIH1cbiAgICB1c2VFZmZlY3QocmVuZGVyQW5ub3RhdGlvbkxheWVyLCBcbiAgICAvLyBPbW1pdHRlZCBjYWxsYmFja3Mgc28gdGhleSBhcmUgbm90IGNhbGxlZCBldmVyeSB0aW1lIHRoZXkgY2hhbmdlXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICAgIFthbm5vdGF0aW9ucywgaW1hZ2VSZXNvdXJjZXNQYXRoLCBsaW5rU2VydmljZSwgcGFnZSwgcmVuZGVyRm9ybXMsIHZpZXdwb3J0XSk7XG4gICAgcmV0dXJuIChSZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHsgY2xhc3NOYW1lOiBjbHN4KCdyZWFjdC1wZGZfX1BhZ2VfX2Fubm90YXRpb25zJywgJ2Fubm90YXRpb25MYXllcicpLCByZWY6IGxheWVyRWxlbWVudCB9KSk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/PageCanvas.js":
/*!************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/PageCanvas.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageCanvas)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var merge_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! merge-refs */ \"(ssr)/./node_modules/merge-refs/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var _pdfjs_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../pdfjs.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/pdfjs.js\");\n/* harmony import */ var _StructTree_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../StructTree.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/StructTree.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n'use client';\n\n\n\n\n\n\n\n\nconst ANNOTATION_MODE = _pdfjs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].AnnotationMode;\nfunction PageCanvas(props) {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const mergedProps = Object.assign(Object.assign({}, pageContext), props);\n    const { _className, canvasBackground, devicePixelRatio = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.getDevicePixelRatio)(), onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, page, renderForms, renderTextLayer, rotate, scale, } = mergedProps;\n    const { canvasRef } = props;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(page, 'Attempted to render page canvas, but no page was specified.');\n    const canvasElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    /**\n     * Called when a page is rendered successfully.\n     */\n    function onRenderSuccess() {\n        if (!page) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onRenderSuccessProps) {\n            onRenderSuccessProps((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.makePageCallback)(page, scale));\n        }\n    }\n    /**\n     * Called when a page fails to render.\n     */\n    function onRenderError(error) {\n        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isCancelException)(error)) {\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_2__(false, error.toString());\n        if (onRenderErrorProps) {\n            onRenderErrorProps(error);\n        }\n    }\n    const renderViewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => page.getViewport({ scale: scale * devicePixelRatio, rotation: rotate }), [devicePixelRatio, page, rotate, scale]);\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => page.getViewport({ scale, rotation: rotate }), [page, rotate, scale]);\n    function drawPageOnCanvas() {\n        if (!page) {\n            return;\n        }\n        // Ensures the canvas will be re-rendered from scratch. Otherwise all form data will stay.\n        page.cleanup();\n        const { current: canvas } = canvasElement;\n        if (!canvas) {\n            return;\n        }\n        canvas.width = renderViewport.width;\n        canvas.height = renderViewport.height;\n        canvas.style.width = `${Math.floor(viewport.width)}px`;\n        canvas.style.height = `${Math.floor(viewport.height)}px`;\n        canvas.style.visibility = 'hidden';\n        const renderContext = {\n            annotationMode: renderForms ? ANNOTATION_MODE.ENABLE_FORMS : ANNOTATION_MODE.ENABLE,\n            canvasContext: canvas.getContext('2d', { alpha: false }),\n            viewport: renderViewport,\n        };\n        if (canvasBackground) {\n            renderContext.background = canvasBackground;\n        }\n        const cancellable = page.render(renderContext);\n        const runningTask = cancellable;\n        cancellable.promise\n            .then(() => {\n            canvas.style.visibility = '';\n            onRenderSuccess();\n        })\n            .catch(onRenderError);\n        return () => (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.cancelRunningTask)(runningTask);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(drawPageOnCanvas, \n    // Ommitted callbacks so they are not called every time they change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        canvasBackground,\n        canvasElement,\n        devicePixelRatio,\n        page,\n        renderForms,\n        renderViewport,\n        viewport,\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n        const { current: canvas } = canvasElement;\n        /**\n         * Zeroing the width and height cause most browsers to release graphics\n         * resources immediately, which can greatly reduce memory consumption.\n         */\n        if (canvas) {\n            canvas.width = 0;\n            canvas.height = 0;\n        }\n    }, [canvasElement]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => cleanup, [cleanup]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"canvas\", { className: `${_className}__canvas`, dir: \"ltr\", ref: (0,merge_refs__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(canvasRef, canvasElement), style: {\n            display: 'block',\n            userSelect: 'none',\n        } }, renderTextLayer ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(_StructTree_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], null) : null));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/PageCanvas.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/PageSVG.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/PageSVG.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageSVG)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var _pdfjs_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../pdfjs.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/pdfjs.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n\n\n\n\n\n\n\n\nfunction PageSVG() {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const { _className, onRenderSuccess: onRenderSuccessProps, onRenderError: onRenderErrorProps, page, rotate, scale, } = pageContext;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(page, 'Attempted to render page SVG, but no page was specified.');\n    const [svgState, svgDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const { value: svg, error: svgError } = svgState;\n    /**\n     * Called when a page is rendered successfully\n     */\n    function onRenderSuccess() {\n        if (!page) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onRenderSuccessProps) {\n            onRenderSuccessProps((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.makePageCallback)(page, scale));\n        }\n    }\n    /**\n     * Called when a page fails to render\n     */\n    function onRenderError() {\n        if (!svgError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isCancelException)(svgError)) {\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_2__(false, svgError.toString());\n        if (onRenderErrorProps) {\n            onRenderErrorProps(svgError);\n        }\n    }\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => page.getViewport({ scale, rotation: rotate }), [page, rotate, scale]);\n    function resetSVG() {\n        svgDispatch({ type: 'RESET' });\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(resetSVG, [page, svgDispatch, viewport]);\n    function renderSVG() {\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(page.getOperatorList());\n        cancellable.promise\n            .then((operatorList) => {\n            const svgGfx = new _pdfjs_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"].SVGGraphics(page.commonObjs, page.objs);\n            svgGfx\n                .getSVG(operatorList, viewport)\n                .then((nextSvg) => {\n                // See https://github.com/mozilla/pdf.js/issues/16745\n                if (!(nextSvg instanceof SVGElement)) {\n                    throw new Error('getSVG returned unexpected result.');\n                }\n                svgDispatch({ type: 'RESOLVE', value: nextSvg });\n            })\n                .catch((error) => {\n                svgDispatch({ type: 'REJECT', error });\n            });\n        })\n            .catch((error) => {\n            svgDispatch({ type: 'REJECT', error });\n        });\n        return () => (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.cancelRunningTask)(cancellable);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(renderSVG, [page, svgDispatch, viewport]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (svg === undefined) {\n            return;\n        }\n        if (svg === false) {\n            onRenderError();\n            return;\n        }\n        onRenderSuccess();\n    }, \n    // Ommitted callbacks so they are not called every time they change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [svg]);\n    function drawPageOnContainer(element) {\n        if (!element || !svg) {\n            return;\n        }\n        // Append SVG element to the main container, if this hasn't been done already\n        if (!element.firstElementChild) {\n            element.appendChild(svg);\n        }\n        const { width, height } = viewport;\n        svg.setAttribute('width', `${width}`);\n        svg.setAttribute('height', `${height}`);\n    }\n    const { width, height } = viewport;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: `${_className}__svg`, \n        // Note: This cannot be shortened, as we need this function to be called with each render.\n        ref: (ref) => drawPageOnContainer(ref), style: {\n            display: 'block',\n            backgroundColor: 'white',\n            overflow: 'hidden',\n            width,\n            height,\n            userSelect: 'none',\n        } }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/PageSVG.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/TextLayer.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextLayer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var _pdfjs_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../pdfjs.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/pdfjs.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n'use client';\n\n\n\n\n\n\n\n\n\nfunction isTextItem(item) {\n    return 'str' in item;\n}\nfunction TextLayer() {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const { customTextRenderer, onGetTextError, onGetTextSuccess, onRenderTextLayerError, onRenderTextLayerSuccess, page, pageIndex, pageNumber, rotate, scale, } = pageContext;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(page, 'Attempted to load page text content, but no page was specified.');\n    const [textContentState, textContentDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { value: textContent, error: textContentError } = textContentState;\n    const layerElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const endElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    warning__WEBPACK_IMPORTED_MODULE_3__(parseInt(window.getComputedStyle(document.body).getPropertyValue('--react-pdf-text-layer'), 10) === 1, 'TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer');\n    /**\n     * Called when a page text content is read successfully\n     */\n    function onLoadSuccess() {\n        if (!textContent) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetTextSuccess) {\n            onGetTextSuccess(textContent);\n        }\n    }\n    /**\n     * Called when a page text content failed to read successfully\n     */\n    function onLoadError() {\n        if (!textContentError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, textContentError.toString());\n        if (onGetTextError) {\n            onGetTextError(textContentError);\n        }\n    }\n    function resetTextContent() {\n        textContentDispatch({ type: 'RESET' });\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(resetTextContent, [page, textContentDispatch]);\n    function loadTextContent() {\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(page.getTextContent());\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextTextContent) => {\n            textContentDispatch({ type: 'RESOLVE', value: nextTextContent });\n        })\n            .catch((error) => {\n            textContentDispatch({ type: 'REJECT', error });\n        });\n        return () => (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.cancelRunningTask)(runningTask);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(loadTextContent, [page, textContentDispatch]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (textContent === undefined) {\n            return;\n        }\n        if (textContent === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, \n    // Ommitted callbacks so they are not called every time they change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [textContent]);\n    /**\n     * Called when a text layer is rendered successfully\n     */\n    const onRenderSuccess = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n        if (onRenderTextLayerSuccess) {\n            onRenderTextLayerSuccess();\n        }\n    }, [onRenderTextLayerSuccess]);\n    /**\n     * Called when a text layer failed to render successfully\n     */\n    const onRenderError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((error) => {\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, error.toString());\n        if (onRenderTextLayerError) {\n            onRenderTextLayerError(error);\n        }\n    }, [onRenderTextLayerError]);\n    function onMouseDown() {\n        const end = endElement.current;\n        if (!end) {\n            return;\n        }\n        end.classList.add('active');\n    }\n    function onMouseUp() {\n        const end = endElement.current;\n        if (!end) {\n            return;\n        }\n        end.classList.remove('active');\n    }\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => page.getViewport({ scale, rotation: rotate }), [page, rotate, scale]);\n    function renderTextLayer() {\n        if (!page || !textContent) {\n            return;\n        }\n        const { current: layer } = layerElement;\n        if (!layer) {\n            return;\n        }\n        layer.innerHTML = '';\n        const textContentSource = page.streamTextContent({ includeMarkedContent: true });\n        const parameters = {\n            container: layer,\n            textContentSource,\n            viewport,\n        };\n        const cancellable = _pdfjs_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"].renderTextLayer(parameters);\n        const runningTask = cancellable;\n        cancellable.promise\n            .then(() => {\n            const end = document.createElement('div');\n            end.className = 'endOfContent';\n            layer.append(end);\n            endElement.current = end;\n            const layerChildren = layer.querySelectorAll('[role=\"presentation\"]');\n            if (customTextRenderer) {\n                let index = 0;\n                textContent.items.forEach((item, itemIndex) => {\n                    if (!isTextItem(item)) {\n                        return;\n                    }\n                    const child = layerChildren[index];\n                    if (!child) {\n                        return;\n                    }\n                    const content = customTextRenderer(Object.assign({ pageIndex,\n                        pageNumber,\n                        itemIndex }, item));\n                    child.innerHTML = content;\n                    index += item.str && item.hasEOL ? 2 : 1;\n                });\n            }\n            // Intentional immediate callback\n            onRenderSuccess();\n        })\n            .catch(onRenderError);\n        return () => (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.cancelRunningTask)(runningTask);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(renderTextLayer, [\n        customTextRenderer,\n        onRenderError,\n        onRenderSuccess,\n        page,\n        pageIndex,\n        pageNumber,\n        textContent,\n        viewport,\n    ]);\n    return (\n    // eslint-disable-next-line jsx-a11y/no-static-element-interactions\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('react-pdf__Page__textContent', 'textLayer'), onMouseUp: onMouseUp, onMouseDown: onMouseDown, ref: layerElement }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/PageContext.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n'use client';\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2VDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDc0M7QUFDdEMsaUVBQWUsb0RBQWEsTUFBTSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGRmZWRpdC8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vUGFnZUNvbnRleHQuanM/ZGU4ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuZXhwb3J0IGRlZmF1bHQgY3JlYXRlQ29udGV4dChudWxsKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/PasswordResponses.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/PasswordResponses.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// As defined in https://github.com/mozilla/pdf.js/blob/d9fac3459609a807be6506fb3441b5da4b154d14/src/shared/util.js#L371-L374\nconst PasswordResponses = {\n    NEED_PASSWORD: 1,\n    INCORRECT_PASSWORD: 2,\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PasswordResponses);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1Bhc3N3b3JkUmVzcG9uc2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsaUJBQWlCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZGZlZGl0Ly4vbm9kZV9tb2R1bGVzL3JlYWN0LXBkZi9kaXN0L2VzbS9QYXNzd29yZFJlc3BvbnNlcy5qcz84N2E1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEFzIGRlZmluZWQgaW4gaHR0cHM6Ly9naXRodWIuY29tL21vemlsbGEvcGRmLmpzL2Jsb2IvZDlmYWMzNDU5NjA5YTgwN2JlNjUwNmZiMzQ0MWI1ZGE0YjE1NGQxNC9zcmMvc2hhcmVkL3V0aWwuanMjTDM3MS1MMzc0XG5jb25zdCBQYXNzd29yZFJlc3BvbnNlcyA9IHtcbiAgICBORUVEX1BBU1NXT1JEOiAxLFxuICAgIElOQ09SUkVDVF9QQVNTV09SRDogMixcbn07XG5leHBvcnQgZGVmYXVsdCBQYXNzd29yZFJlc3BvbnNlcztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/PasswordResponses.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/StructTree.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/StructTree.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StructTree)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var _StructTreeItem_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./StructTreeItem.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/StructTreeItem.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n\n\n\n\n\n\n\n\nfunction StructTree() {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const { onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, } = pageContext;\n    const [structTreeState, structTreeDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const { value: structTree, error: structTreeError } = structTreeState;\n    const { customTextRenderer, page } = pageContext;\n    function onLoadSuccess() {\n        if (!structTree) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetStructTreeSuccessProps) {\n            onGetStructTreeSuccessProps(structTree);\n        }\n    }\n    function onLoadError() {\n        if (!structTreeError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_2__(false, structTreeError.toString());\n        if (onGetStructTreeErrorProps) {\n            onGetStructTreeErrorProps(structTreeError);\n        }\n    }\n    function resetAnnotations() {\n        structTreeDispatch({ type: 'RESET' });\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(resetAnnotations, [structTreeDispatch, page]);\n    function loadStructTree() {\n        if (customTextRenderer) {\n            // TODO: Document why this is necessary\n            return;\n        }\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(page.getStructTree());\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextStructTree) => {\n            structTreeDispatch({ type: 'RESOLVE', value: nextStructTree });\n        })\n            .catch((error) => {\n            structTreeDispatch({ type: 'REJECT', error });\n        });\n        return () => (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.cancelRunningTask)(runningTask);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(loadStructTree, [customTextRenderer, page, structTreeDispatch]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (structTree === undefined) {\n            return;\n        }\n        if (structTree === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, \n    // Ommitted callbacks so they are not called every time they change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [structTree]);\n    if (!structTree) {\n        return null;\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_StructTreeItem_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], { className: \"react-pdf__Page__structTree structTree\", node: structTree });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/StructTree.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/StructTreeItem.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/StructTreeItem.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StructTreeItem)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./shared/structTreeUtils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js\");\n\n\nfunction StructTreeItem({ className, node }) {\n    const attributes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_1__.getAttributes)(node), [node]);\n    const children = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        if (!(0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isStructTreeNode)(node)) {\n            return null;\n        }\n        if ((0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_1__.isStructTreeNodeWithOnlyContentChild)(node)) {\n            return null;\n        }\n        return node.children.map((child, index) => {\n            return (\n            // eslint-disable-next-line react/no-array-index-key\n            react__WEBPACK_IMPORTED_MODULE_0__.createElement(StructTreeItem, { key: index, node: child }));\n        });\n    }, [node]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", Object.assign({ className: className }, attributes), children));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1N0cnVjdFRyZWVJdGVtLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1QztBQUM4RTtBQUN0RywwQkFBMEIsaUJBQWlCO0FBQzFELHVCQUF1Qiw4Q0FBTyxPQUFPLHlFQUFhO0FBQ2xELHFCQUFxQiw4Q0FBTztBQUM1QixhQUFhLDRFQUFnQjtBQUM3QjtBQUNBO0FBQ0EsWUFBWSxnR0FBb0M7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksZ0RBQW1CLG1CQUFtQix5QkFBeUI7QUFDM0UsU0FBUztBQUNULEtBQUs7QUFDTCxZQUFZLGdEQUFtQix5QkFBeUIsc0JBQXNCO0FBQzlFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGRmZWRpdC8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vU3RydWN0VHJlZUl0ZW0uanM/M2VlMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGdldEF0dHJpYnV0ZXMsIGlzU3RydWN0VHJlZU5vZGUsIGlzU3RydWN0VHJlZU5vZGVXaXRoT25seUNvbnRlbnRDaGlsZCwgfSBmcm9tICcuL3NoYXJlZC9zdHJ1Y3RUcmVlVXRpbHMuanMnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU3RydWN0VHJlZUl0ZW0oeyBjbGFzc05hbWUsIG5vZGUgfSkge1xuICAgIGNvbnN0IGF0dHJpYnV0ZXMgPSB1c2VNZW1vKCgpID0+IGdldEF0dHJpYnV0ZXMobm9kZSksIFtub2RlXSk7XG4gICAgY29uc3QgY2hpbGRyZW4gPSB1c2VNZW1vKCgpID0+IHtcbiAgICAgICAgaWYgKCFpc1N0cnVjdFRyZWVOb2RlKG5vZGUpKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoaXNTdHJ1Y3RUcmVlTm9kZVdpdGhPbmx5Q29udGVudENoaWxkKG5vZGUpKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbm9kZS5jaGlsZHJlbi5tYXAoKGNoaWxkLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC9uby1hcnJheS1pbmRleC1rZXlcbiAgICAgICAgICAgIFJlYWN0LmNyZWF0ZUVsZW1lbnQoU3RydWN0VHJlZUl0ZW0sIHsga2V5OiBpbmRleCwgbm9kZTogY2hpbGQgfSkpO1xuICAgICAgICB9KTtcbiAgICB9LCBbbm9kZV0pO1xuICAgIHJldHVybiAoUmVhY3QuY3JlYXRlRWxlbWVudChcInNwYW5cIiwgT2JqZWN0LmFzc2lnbih7IGNsYXNzTmFtZTogY2xhc3NOYW1lIH0sIGF0dHJpYnV0ZXMpLCBjaGlsZHJlbikpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/StructTreeItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/pdfjs.js":
/*!**************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/pdfjs.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var pdfjs_dist__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pdfjs-dist */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.js\");\n\nconst pdfjs = ( true ? pdfjs_dist__WEBPACK_IMPORTED_MODULE_0__ : /*#__PURE__*/ (pdfjs_dist__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (pdfjs_dist__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(pdfjs_dist__WEBPACK_IMPORTED_MODULE_0__, 2))));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pdfjs);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3BkZmpzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEwQztBQUMxQyxlQUFlLEtBQXdCLEdBQUcsdUNBQXNCLEdBQUcsd01BQVc7QUFDOUUsaUVBQWUsS0FBSyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGRmZWRpdC8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vcGRmanMuanM/ODVhOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBwZGZqc01vZHVsZSBmcm9tICdwZGZqcy1kaXN0JztcbmNvbnN0IHBkZmpzID0gKCdkZWZhdWx0JyBpbiBwZGZqc01vZHVsZSA/IHBkZmpzTW9kdWxlWydkZWZhdWx0J10gOiBwZGZqc01vZHVsZSk7XG5leHBvcnQgZGVmYXVsdCBwZGZqcztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/pdfjs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADING_PATTERN: () => (/* binding */ HEADING_PATTERN),\n/* harmony export */   PDF_ROLE_TO_HTML_ROLE: () => (/* binding */ PDF_ROLE_TO_HTML_ROLE)\n/* harmony export */ });\n// From pdfjs-dist/lib/web/struct_tree_layer_builder.js\nconst PDF_ROLE_TO_HTML_ROLE = {\n    // Document level structure types\n    Document: null, // There's a \"document\" role, but it doesn't make sense here.\n    DocumentFragment: null,\n    // Grouping level structure types\n    Part: 'group',\n    Sect: 'group', // XXX: There's a \"section\" role, but it's abstract.\n    Div: 'group',\n    Aside: 'note',\n    NonStruct: 'none',\n    // Block level structure types\n    P: null,\n    // H<n>,\n    H: 'heading',\n    Title: null,\n    FENote: 'note',\n    // Sub-block level structure type\n    Sub: 'group',\n    // General inline level structure types\n    Lbl: null,\n    Span: null,\n    Em: null,\n    Strong: null,\n    Link: 'link',\n    Annot: 'note',\n    Form: 'form',\n    // Ruby and Warichu structure types\n    Ruby: null,\n    RB: null,\n    RT: null,\n    RP: null,\n    Warichu: null,\n    WT: null,\n    WP: null,\n    // List standard structure types\n    L: 'list',\n    LI: 'listitem',\n    LBody: null,\n    // Table standard structure types\n    Table: 'table',\n    TR: 'row',\n    TH: 'columnheader',\n    TD: 'cell',\n    THead: 'columnheader',\n    TBody: null,\n    TFoot: null,\n    // Standard structure type Caption\n    Caption: null,\n    // Standard structure type Figure\n    Figure: 'figure',\n    // Standard structure type Formula\n    Formula: null,\n    // standard structure type Artifact\n    Artifact: null,\n};\nconst HEADING_PATTERN = /^H(\\d+)$/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useDocumentContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _DocumentContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../DocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js\");\n\n\nfunction useDocumentContext() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_DocumentContext_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VEb2N1bWVudENvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW1DO0FBQ29CO0FBQ3hDO0FBQ2YsV0FBVyxpREFBVSxDQUFDLDJEQUFlO0FBQ3JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGRmZWRpdC8uL25vZGVfbW9kdWxlcy9yZWFjdC1wZGYvZGlzdC9lc20vc2hhcmVkL2hvb2tzL3VzZURvY3VtZW50Q29udGV4dC5qcz9iNzY0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgRG9jdW1lbnRDb250ZXh0IGZyb20gJy4uLy4uL0RvY3VtZW50Q29udGV4dC5qcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VEb2N1bWVudENvbnRleHQoKSB7XG4gICAgcmV0dXJuIHVzZUNvbnRleHQoRG9jdW1lbnRDb250ZXh0KTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ usePageContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _PageContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../PageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js\");\n\n\nfunction usePageContext() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_PageContext_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VQYWdlQ29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDWTtBQUNoQztBQUNmLFdBQVcsaURBQVUsQ0FBQyx1REFBVztBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL3BkZmVkaXQvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VQYWdlQ29udGV4dC5qcz9hOGM5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgUGFnZUNvbnRleHQgZnJvbSAnLi4vLi4vUGFnZUNvbnRleHQuanMnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlUGFnZUNvbnRleHQoKSB7XG4gICAgcmV0dXJuIHVzZUNvbnRleHQoUGFnZUNvbnRleHQpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useResolver)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction reducer(state, action) {\n    switch (action.type) {\n        case 'RESOLVE':\n            return { value: action.value, error: undefined };\n        case 'REJECT':\n            return { value: false, error: action.error };\n        case 'RESET':\n            return { value: undefined, error: undefined };\n        default:\n            return state;\n    }\n}\nfunction useResolver() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((reducer), { value: undefined, error: undefined });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VSZXNvbHZlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDZTtBQUNmLFdBQVcsaURBQVUsY0FBYyxvQ0FBb0M7QUFDdkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZGZlZGl0Ly4vbm9kZV9tb2R1bGVzL3JlYWN0LXBkZi9kaXN0L2VzbS9zaGFyZWQvaG9va3MvdXNlUmVzb2x2ZXIuanM/MDVmYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSZWR1Y2VyIH0gZnJvbSAncmVhY3QnO1xuZnVuY3Rpb24gcmVkdWNlcihzdGF0ZSwgYWN0aW9uKSB7XG4gICAgc3dpdGNoIChhY3Rpb24udHlwZSkge1xuICAgICAgICBjYXNlICdSRVNPTFZFJzpcbiAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiBhY3Rpb24udmFsdWUsIGVycm9yOiB1bmRlZmluZWQgfTtcbiAgICAgICAgY2FzZSAnUkVKRUNUJzpcbiAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiBmYWxzZSwgZXJyb3I6IGFjdGlvbi5lcnJvciB9O1xuICAgICAgICBjYXNlICdSRVNFVCc6XG4gICAgICAgICAgICByZXR1cm4geyB2YWx1ZTogdW5kZWZpbmVkLCBlcnJvcjogdW5kZWZpbmVkIH07XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICByZXR1cm4gc3RhdGU7XG4gICAgfVxufVxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlUmVzb2x2ZXIoKSB7XG4gICAgcmV0dXJuIHVzZVJlZHVjZXIoKHJlZHVjZXIpLCB7IHZhbHVlOiB1bmRlZmluZWQsIGVycm9yOiB1bmRlZmluZWQgfSk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/propTypes.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/propTypes.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventProps: () => (/* binding */ eventProps),\n/* harmony export */   isClassName: () => (/* binding */ isClassName),\n/* harmony export */   isFile: () => (/* binding */ isFile),\n/* harmony export */   isLinkService: () => (/* binding */ isLinkService),\n/* harmony export */   isLinkTarget: () => (/* binding */ isLinkTarget),\n/* harmony export */   isPage: () => (/* binding */ isPage),\n/* harmony export */   isPageIndex: () => (/* binding */ isPageIndex),\n/* harmony export */   isPageNumber: () => (/* binding */ isPageNumber),\n/* harmony export */   isPdf: () => (/* binding */ isPdf),\n/* harmony export */   isRef: () => (/* binding */ isRef),\n/* harmony export */   isRenderMode: () => (/* binding */ isRenderMode),\n/* harmony export */   isRotate: () => (/* binding */ isRotate)\n/* harmony export */ });\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var make_event_props__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! make-event-props */ \"(ssr)/./node_modules/make-event-props/dist/esm/index.js\");\n/* harmony import */ var _pdfjs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../pdfjs.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/pdfjs.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _LinkService_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../LinkService.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/LinkService.js\");\n\n\n\n\n\nconst { PDFDataRangeTransport } = _pdfjs_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\nconst eventProps = (() => {\n    const result = {};\n    make_event_props__WEBPACK_IMPORTED_MODULE_1__.allEvents.forEach((eventName) => {\n        result[eventName] = prop_types__WEBPACK_IMPORTED_MODULE_2__.func;\n    });\n    return result;\n})();\nconst isTypedArray = prop_types__WEBPACK_IMPORTED_MODULE_2__.oneOfType([\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(Int8Array),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(Uint8Array),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(Uint8ClampedArray),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(Int16Array),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(Uint16Array),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(Int32Array),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(Uint32Array),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(Float32Array),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(Float64Array),\n]);\nconst fileTypes = [\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.string,\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(ArrayBuffer),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.shape({\n        data: prop_types__WEBPACK_IMPORTED_MODULE_2__.oneOfType([\n            prop_types__WEBPACK_IMPORTED_MODULE_2__.string,\n            prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(ArrayBuffer),\n            prop_types__WEBPACK_IMPORTED_MODULE_2__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_2__.number.isRequired),\n            isTypedArray,\n        ]).isRequired,\n    }),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.shape({\n        range: prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(PDFDataRangeTransport).isRequired,\n    }),\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.shape({\n        url: prop_types__WEBPACK_IMPORTED_MODULE_2__.string.isRequired,\n    }),\n];\nif (typeof Blob !== 'undefined') {\n    fileTypes.push(prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(Blob));\n}\nconst isClassName = prop_types__WEBPACK_IMPORTED_MODULE_2__.oneOfType([\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.string,\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_2__.string),\n]);\nconst isFile = prop_types__WEBPACK_IMPORTED_MODULE_2__.oneOfType(fileTypes);\nconst isLinkService = prop_types__WEBPACK_IMPORTED_MODULE_2__.instanceOf(_LinkService_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]);\nconst isLinkTarget = prop_types__WEBPACK_IMPORTED_MODULE_2__.oneOf(['_self', '_blank', '_parent', '_top']);\nconst isPage = prop_types__WEBPACK_IMPORTED_MODULE_2__.shape({\n    commonObjs: prop_types__WEBPACK_IMPORTED_MODULE_2__.shape({}).isRequired,\n    getAnnotations: prop_types__WEBPACK_IMPORTED_MODULE_2__.func.isRequired,\n    getTextContent: prop_types__WEBPACK_IMPORTED_MODULE_2__.func.isRequired,\n    getViewport: prop_types__WEBPACK_IMPORTED_MODULE_2__.func.isRequired,\n    render: prop_types__WEBPACK_IMPORTED_MODULE_2__.func.isRequired,\n});\nconst isPageIndex = function isPageIndex(props, propName, componentName) {\n    const { [propName]: pageIndex, pageNumber, pdf } = props;\n    if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.isDefined)(pdf)) {\n        return null;\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.isDefined)(pageIndex)) {\n        if (typeof pageIndex !== 'number') {\n            return new Error(`\\`${propName}\\` of type \\`${typeof pageIndex}\\` supplied to \\`${componentName}\\`, expected \\`number\\`.`);\n        }\n        if (pageIndex < 0) {\n            return new Error(`Expected \\`${propName}\\` to be greater or equal to 0.`);\n        }\n        const { numPages } = pdf;\n        if (pageIndex + 1 > numPages) {\n            return new Error(`Expected \\`${propName}\\` to be less or equal to ${numPages - 1}.`);\n        }\n    }\n    else if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.isDefined)(pageNumber)) {\n        return new Error(`\\`${propName}\\` not supplied. Either pageIndex or pageNumber must be supplied to \\`${componentName}\\`.`);\n    }\n    // Everything is fine\n    return null;\n};\nconst isPageNumber = function isPageNumber(props, propName, componentName) {\n    const { [propName]: pageNumber, pageIndex, pdf } = props;\n    if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.isDefined)(pdf)) {\n        return null;\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.isDefined)(pageNumber)) {\n        if (typeof pageNumber !== 'number') {\n            return new Error(`\\`${propName}\\` of type \\`${typeof pageNumber}\\` supplied to \\`${componentName}\\`, expected \\`number\\`.`);\n        }\n        if (pageNumber < 1) {\n            return new Error(`Expected \\`${propName}\\` to be greater or equal to 1.`);\n        }\n        const { numPages } = pdf;\n        if (pageNumber > numPages) {\n            return new Error(`Expected \\`${propName}\\` to be less or equal to ${numPages}.`);\n        }\n    }\n    else if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.isDefined)(pageIndex)) {\n        return new Error(`\\`${propName}\\` not supplied. Either pageIndex or pageNumber must be supplied to \\`${componentName}\\`.`);\n    }\n    // Everything is fine\n    return null;\n};\nconst isPdf = prop_types__WEBPACK_IMPORTED_MODULE_2__.oneOfType([\n    // Ideally, this would be `PropTypes.instanceOf(PDFDocumentProxy)`, but it can't be imported.\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.any,\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.oneOf([false]),\n]);\nconst isRef = prop_types__WEBPACK_IMPORTED_MODULE_2__.oneOfType([\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.func,\n    prop_types__WEBPACK_IMPORTED_MODULE_2__.exact({\n        current: prop_types__WEBPACK_IMPORTED_MODULE_2__.any,\n    }),\n]);\nconst isRenderMode = prop_types__WEBPACK_IMPORTED_MODULE_2__.oneOf(['canvas', 'custom', 'none', 'svg']);\nconst isRotate = prop_types__WEBPACK_IMPORTED_MODULE_2__.oneOf([0, 90, 180, 270]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/propTypes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttributes: () => (/* binding */ getAttributes),\n/* harmony export */   getBaseAttributes: () => (/* binding */ getBaseAttributes),\n/* harmony export */   getRoleAttributes: () => (/* binding */ getRoleAttributes),\n/* harmony export */   isPdfRole: () => (/* binding */ isPdfRole),\n/* harmony export */   isStructTreeNode: () => (/* binding */ isStructTreeNode),\n/* harmony export */   isStructTreeNodeWithOnlyContentChild: () => (/* binding */ isStructTreeNodeWithOnlyContentChild)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/constants.js\");\n\nfunction isPdfRole(role) {\n    return role in _constants_js__WEBPACK_IMPORTED_MODULE_0__.PDF_ROLE_TO_HTML_ROLE;\n}\nfunction isStructTreeNode(node) {\n    return 'children' in node;\n}\nfunction isStructTreeNodeWithOnlyContentChild(node) {\n    if (!isStructTreeNode(node)) {\n        return false;\n    }\n    return node.children.length === 1 && 0 in node.children && 'id' in node.children[0];\n}\nfunction getRoleAttributes(node) {\n    const attributes = {};\n    if (isStructTreeNode(node)) {\n        const { role } = node;\n        const matches = role.match(_constants_js__WEBPACK_IMPORTED_MODULE_0__.HEADING_PATTERN);\n        if (matches) {\n            attributes.role = 'heading';\n            attributes['aria-level'] = Number(matches[1]);\n        }\n        else if (isPdfRole(role)) {\n            const htmlRole = _constants_js__WEBPACK_IMPORTED_MODULE_0__.PDF_ROLE_TO_HTML_ROLE[role];\n            if (htmlRole) {\n                attributes.role = htmlRole;\n            }\n        }\n    }\n    return attributes;\n}\nfunction getBaseAttributes(node) {\n    const attributes = {};\n    if (isStructTreeNode(node)) {\n        if (node.alt !== undefined) {\n            attributes['aria-label'] = node.alt;\n        }\n        if (node.lang !== undefined) {\n            attributes.lang = node.lang;\n        }\n        if (isStructTreeNodeWithOnlyContentChild(node)) {\n            const [child] = node.children;\n            if (child) {\n                const childAttributes = getBaseAttributes(child);\n                return Object.assign(Object.assign({}, attributes), childAttributes);\n            }\n        }\n    }\n    else {\n        if ('id' in node) {\n            attributes['aria-owns'] = node.id;\n        }\n    }\n    return attributes;\n}\nfunction getAttributes(node) {\n    if (!node) {\n        return null;\n    }\n    return Object.assign(Object.assign({}, getRoleAttributes(node)), getBaseAttributes(node));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelRunningTask: () => (/* binding */ cancelRunningTask),\n/* harmony export */   dataURItoByteString: () => (/* binding */ dataURItoByteString),\n/* harmony export */   displayCORSWarning: () => (/* binding */ displayCORSWarning),\n/* harmony export */   displayWorkerWarning: () => (/* binding */ displayWorkerWarning),\n/* harmony export */   getDevicePixelRatio: () => (/* binding */ getDevicePixelRatio),\n/* harmony export */   isArrayBuffer: () => (/* binding */ isArrayBuffer),\n/* harmony export */   isBlob: () => (/* binding */ isBlob),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isCancelException: () => (/* binding */ isCancelException),\n/* harmony export */   isDataURI: () => (/* binding */ isDataURI),\n/* harmony export */   isDefined: () => (/* binding */ isDefined),\n/* harmony export */   isLocalFileSystem: () => (/* binding */ isLocalFileSystem),\n/* harmony export */   isProvided: () => (/* binding */ isProvided),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   loadFromFile: () => (/* binding */ loadFromFile),\n/* harmony export */   makePageCallback: () => (/* binding */ makePageCallback)\n/* harmony export */ });\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n\n\n/**\n * Checks if we're running in a browser environment.\n */\nconst isBrowser = typeof document !== 'undefined';\n/**\n * Checks whether we're running from a local file system.\n */\nconst isLocalFileSystem = isBrowser && window.location.protocol === 'file:';\n/**\n * Checks whether a variable is defined.\n *\n * @param {*} variable Variable to check\n */\nfunction isDefined(variable) {\n    return typeof variable !== 'undefined';\n}\n/**\n * Checks whether a variable is defined and not null.\n *\n * @param {*} variable Variable to check\n */\nfunction isProvided(variable) {\n    return isDefined(variable) && variable !== null;\n}\n/**\n * Checks whether a variable provided is a string.\n *\n * @param {*} variable Variable to check\n */\nfunction isString(variable) {\n    return typeof variable === 'string';\n}\n/**\n * Checks whether a variable provided is an ArrayBuffer.\n *\n * @param {*} variable Variable to check\n */\nfunction isArrayBuffer(variable) {\n    return variable instanceof ArrayBuffer;\n}\n/**\n * Checks whether a variable provided is a Blob.\n *\n * @param {*} variable Variable to check\n */\nfunction isBlob(variable) {\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(isBrowser, 'isBlob can only be used in a browser environment');\n    return variable instanceof Blob;\n}\n/**\n * Checks whether a variable provided is a data URI.\n *\n * @param {*} variable String to check\n */\nfunction isDataURI(variable) {\n    return isString(variable) && /^data:/.test(variable);\n}\nfunction dataURItoByteString(dataURI) {\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(isDataURI(dataURI), 'Invalid data URI.');\n    const [headersString = '', dataString = ''] = dataURI.split(',');\n    const headers = headersString.split(';');\n    if (headers.indexOf('base64') !== -1) {\n        return atob(dataString);\n    }\n    return unescape(dataString);\n}\nfunction getDevicePixelRatio() {\n    return (isBrowser && window.devicePixelRatio) || 1;\n}\nconst allowFileAccessFromFilesTip = 'On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.';\nfunction displayCORSWarning() {\n    warning__WEBPACK_IMPORTED_MODULE_1__(!isLocalFileSystem, `Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`);\n}\nfunction displayWorkerWarning() {\n    warning__WEBPACK_IMPORTED_MODULE_1__(!isLocalFileSystem, `Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`);\n}\nfunction cancelRunningTask(runningTask) {\n    if (runningTask && runningTask.cancel)\n        runningTask.cancel();\n}\nfunction makePageCallback(page, scale) {\n    Object.defineProperty(page, 'width', {\n        get() {\n            return this.view[2] * scale;\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'height', {\n        get() {\n            return this.view[3] * scale;\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'originalWidth', {\n        get() {\n            return this.view[2];\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'originalHeight', {\n        get() {\n            return this.view[3];\n        },\n        configurable: true,\n    });\n    return page;\n}\nfunction isCancelException(error) {\n    return error.name === 'RenderingCancelledException';\n}\nfunction loadFromFile(file) {\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = () => {\n            if (!reader.result) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            resolve(reader.result);\n        };\n        reader.onerror = (event) => {\n            if (!event.target) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            const { error } = event.target;\n            if (!error) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            switch (error.code) {\n                case error.NOT_FOUND_ERR:\n                    return reject(new Error('Error while reading a file: File not found.'));\n                case error.SECURITY_ERR:\n                    return reject(new Error('Error while reading a file: Security error.'));\n                case error.ABORT_ERR:\n                    return reject(new Error('Error while reading a file: Aborted.'));\n                default:\n                    return reject(new Error('Error while reading a file.'));\n            }\n        };\n        reader.readAsArrayBuffer(file);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\n");

/***/ })

};
;