"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/make-event-props";
exports.ids = ["vendor-chunks/make-event-props"];
exports.modules = {

/***/ "(ssr)/./node_modules/make-event-props/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/make-event-props/dist/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allEvents: () => (/* binding */ allEvents),\n/* harmony export */   animationEvents: () => (/* binding */ animationEvents),\n/* harmony export */   changeEvents: () => (/* binding */ changeEvents),\n/* harmony export */   clipboardEvents: () => (/* binding */ clipboardEvents),\n/* harmony export */   compositionEvents: () => (/* binding */ compositionEvents),\n/* harmony export */   \"default\": () => (/* binding */ makeEventProps),\n/* harmony export */   dragEvents: () => (/* binding */ dragEvents),\n/* harmony export */   focusEvents: () => (/* binding */ focusEvents),\n/* harmony export */   formEvents: () => (/* binding */ formEvents),\n/* harmony export */   imageEvents: () => (/* binding */ imageEvents),\n/* harmony export */   keyboardEvents: () => (/* binding */ keyboardEvents),\n/* harmony export */   mediaEvents: () => (/* binding */ mediaEvents),\n/* harmony export */   mouseEvents: () => (/* binding */ mouseEvents),\n/* harmony export */   otherEvents: () => (/* binding */ otherEvents),\n/* harmony export */   pointerEvents: () => (/* binding */ pointerEvents),\n/* harmony export */   selectionEvents: () => (/* binding */ selectionEvents),\n/* harmony export */   touchEvents: () => (/* binding */ touchEvents),\n/* harmony export */   transitionEvents: () => (/* binding */ transitionEvents),\n/* harmony export */   uiEvents: () => (/* binding */ uiEvents),\n/* harmony export */   wheelEvents: () => (/* binding */ wheelEvents)\n/* harmony export */ });\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n// As defined on the list of supported events: https://reactjs.org/docs/events.html\nvar clipboardEvents = ['onCopy', 'onCut', 'onPaste'];\nvar compositionEvents = [\n    'onCompositionEnd',\n    'onCompositionStart',\n    'onCompositionUpdate',\n];\nvar focusEvents = ['onFocus', 'onBlur'];\nvar formEvents = ['onInput', 'onInvalid', 'onReset', 'onSubmit'];\nvar imageEvents = ['onLoad', 'onError'];\nvar keyboardEvents = ['onKeyDown', 'onKeyPress', 'onKeyUp'];\nvar mediaEvents = [\n    'onAbort',\n    'onCanPlay',\n    'onCanPlayThrough',\n    'onDurationChange',\n    'onEmptied',\n    'onEncrypted',\n    'onEnded',\n    'onError',\n    'onLoadedData',\n    'onLoadedMetadata',\n    'onLoadStart',\n    'onPause',\n    'onPlay',\n    'onPlaying',\n    'onProgress',\n    'onRateChange',\n    'onSeeked',\n    'onSeeking',\n    'onStalled',\n    'onSuspend',\n    'onTimeUpdate',\n    'onVolumeChange',\n    'onWaiting',\n];\nvar mouseEvents = [\n    'onClick',\n    'onContextMenu',\n    'onDoubleClick',\n    'onMouseDown',\n    'onMouseEnter',\n    'onMouseLeave',\n    'onMouseMove',\n    'onMouseOut',\n    'onMouseOver',\n    'onMouseUp',\n];\nvar dragEvents = [\n    'onDrag',\n    'onDragEnd',\n    'onDragEnter',\n    'onDragExit',\n    'onDragLeave',\n    'onDragOver',\n    'onDragStart',\n    'onDrop',\n];\nvar selectionEvents = ['onSelect'];\nvar touchEvents = ['onTouchCancel', 'onTouchEnd', 'onTouchMove', 'onTouchStart'];\nvar pointerEvents = [\n    'onPointerDown',\n    'onPointerMove',\n    'onPointerUp',\n    'onPointerCancel',\n    'onGotPointerCapture',\n    'onLostPointerCapture',\n    'onPointerEnter',\n    'onPointerLeave',\n    'onPointerOver',\n    'onPointerOut',\n];\nvar uiEvents = ['onScroll'];\nvar wheelEvents = ['onWheel'];\nvar animationEvents = [\n    'onAnimationStart',\n    'onAnimationEnd',\n    'onAnimationIteration',\n];\nvar transitionEvents = ['onTransitionEnd'];\nvar otherEvents = ['onToggle'];\nvar changeEvents = ['onChange'];\nvar allEvents = __spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray([], clipboardEvents, true), compositionEvents, true), focusEvents, true), formEvents, true), imageEvents, true), keyboardEvents, true), mediaEvents, true), mouseEvents, true), dragEvents, true), selectionEvents, true), touchEvents, true), pointerEvents, true), uiEvents, true), wheelEvents, true), animationEvents, true), transitionEvents, true), changeEvents, true), otherEvents, true);\n/**\n * Returns an object with on-event callback props curried with provided args.\n * @param {Object} props Props passed to a component.\n * @param {Function=} getArgs A function that returns argument(s) on-event callbacks\n *   shall be curried with.\n */\nfunction makeEventProps(props, getArgs) {\n    var eventProps = {};\n    allEvents.forEach(function (eventName) {\n        var eventHandler = props[eventName];\n        if (!eventHandler) {\n            return;\n        }\n        if (getArgs) {\n            eventProps[eventName] = (function (event) {\n                return eventHandler(event, getArgs(eventName));\n            });\n        }\n        else {\n            eventProps[eventName] = eventHandler;\n        }\n    });\n    return eventProps;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/make-event-props/dist/esm/index.js\n");

/***/ })

};
;