/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?8412":
/*!************************!*\
  !*** canvas (ignored) ***!
  \************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZTeWtvdC1NYWMlMkZ2YXIlMkZ3d3clMkZodG1sJTJGa2xheV90b29scyUyRnBkZmVkaXQlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZGZlZGl0Lz8xNDA2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL1N5a290LU1hYy92YXIvd3d3L2h0bWwva2xheV90b29scy9wZGZlZGl0L3NyYy9hcHAvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_PDFEditor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/PDFEditor */ \"(ssr)/./src/components/PDFEditor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PDFEditor__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/app/page.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/app/page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRStDO0FBRWhDLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDSCw2REFBU0E7Ozs7Ozs7Ozs7QUFHaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZGZlZGl0Ly4vc3JjL2FwcC9wYWdlLnRzeD9mNjhhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFBERkVkaXRvciBmcm9tICdAL2NvbXBvbmVudHMvUERGRWRpdG9yJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICA8UERGRWRpdG9yIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUERGRWRpdG9yIiwiSG9tZSIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FileUpload.tsx":
/*!***************************************!*\
  !*** ./src/components/FileUpload.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst FileUpload = ({ onFileUpload })=>{\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const validateFile = (file)=>{\n        if (file.type !== \"application/pdf\") {\n            setError(\"Please upload a PDF file only.\");\n            return false;\n        }\n        if (file.size > 50 * 1024 * 1024) {\n            setError(\"File size must be less than 50MB.\");\n            return false;\n        }\n        setError(null);\n        return true;\n    };\n    const handleFileSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((file)=>{\n        if (validateFile(file)) {\n            onFileUpload(file);\n        }\n    }, [\n        onFileUpload\n    ]);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            handleFileSelect(files[0]);\n        }\n    }, [\n        handleFileSelect\n    ]);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    }, []);\n    const handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    }, []);\n    const handleFileInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            handleFileSelect(files[0]);\n        }\n    }, [\n        handleFileSelect\n    ]);\n    const clearError = ()=>{\n        setError(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-2xl mx-auto p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          relative border-2 border-dashed rounded-lg p-12 text-center transition-colors\n          ${isDragOver ? \"border-blue-400 bg-blue-50\" : \"border-gray-300 hover:border-gray-400\"}\n        `,\n                onDrop: handleDrop,\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"file\",\n                        accept: \".pdf,application/pdf\",\n                        onChange: handleFileInputChange,\n                        className: \"absolute inset-0 w-full h-full opacity-0 cursor-pointer\",\n                        id: \"file-upload\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-8 h-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"Upload your PDF file\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 mb-4\",\n                                        children: \"Drag and drop your PDF here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"file-upload\",\n                                        className: \"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Choose PDF File\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Supported format: PDF\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Maximum file size: 50MB\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mr-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-3 h-3 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearError,\n                        className: \"text-red-600 hover:text-red-800 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/FileUpload.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FileUpload);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FileUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PDFEditor.tsx":
/*!**************************************!*\
  !*** ./src/components/PDFEditor.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_pdf__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-pdf */ \"(ssr)/./node_modules/react-pdf/dist/esm/pdfjs.js\");\n/* harmony import */ var react_pdf_dist_Page_AnnotationLayer_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-pdf/dist/Page/AnnotationLayer.css */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css\");\n/* harmony import */ var react_pdf_dist_Page_TextLayer_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-pdf/dist/Page/TextLayer.css */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.css\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Circle,Download,FileText,Highlighter,Image,Square,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Circle,Download,FileText,Highlighter,Image,Square,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Circle,Download,FileText,Highlighter,Image,Square,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/highlighter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Circle,Download,FileText,Highlighter,Image,Square,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Circle,Download,FileText,Highlighter,Image,Square,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Circle,Download,FileText,Highlighter,Image,Square,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Circle,Download,FileText,Highlighter,Image,Square,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Circle,Download,FileText,Highlighter,Image,Square,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Circle,Download,FileText,Highlighter,Image,Square,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Circle,Download,FileText,Highlighter,Image,Square,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _FileUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FileUpload */ \"(ssr)/./src/components/FileUpload.tsx\");\n/* harmony import */ var _PDFViewer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PDFViewer */ \"(ssr)/./src/components/PDFViewer.tsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Toolbar */ \"(ssr)/./src/components/Toolbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n// Configure PDF.js worker with legacy build\nreact_pdf__WEBPACK_IMPORTED_MODULE_7__[\"default\"].GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${react_pdf__WEBPACK_IMPORTED_MODULE_7__[\"default\"].version}/legacy/build/pdf.worker.js`;\nconst PDFEditor = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        file: null,\n        numPages: 0,\n        currentPage: 1,\n        scale: 1.0,\n        rotation: 0,\n        activeTool: \"select\",\n        annotations: []\n    });\n    const tools = [\n        {\n            id: \"select\",\n            name: \"Select\",\n            icon: _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            active: state.activeTool === \"select\"\n        },\n        {\n            id: \"text\",\n            name: \"Text\",\n            icon: _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            active: state.activeTool === \"text\"\n        },\n        {\n            id: \"highlight\",\n            name: \"Highlight\",\n            icon: _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            active: state.activeTool === \"highlight\"\n        },\n        {\n            id: \"rectangle\",\n            name: \"Rectangle\",\n            icon: _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            active: state.activeTool === \"rectangle\"\n        },\n        {\n            id: \"circle\",\n            name: \"Circle\",\n            icon: _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            active: state.activeTool === \"circle\"\n        },\n        {\n            id: \"arrow\",\n            name: \"Arrow\",\n            icon: _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            active: state.activeTool === \"arrow\"\n        },\n        {\n            id: \"image\",\n            name: \"Image\",\n            icon: _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            active: state.activeTool === \"image\"\n        }\n    ];\n    const handleFileUpload = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((file)=>{\n        setState((prev)=>({\n                ...prev,\n                file,\n                currentPage: 1,\n                numPages: 0\n            }));\n    }, []);\n    const onDocumentLoadSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(({ numPages })=>{\n        setState((prev)=>({\n                ...prev,\n                numPages\n            }));\n    }, []);\n    const handleZoomIn = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setState((prev)=>({\n                ...prev,\n                scale: Math.min(prev.scale + 0.2, 3.0)\n            }));\n    }, []);\n    const handleZoomOut = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setState((prev)=>({\n                ...prev,\n                scale: Math.max(prev.scale - 0.2, 0.5)\n            }));\n    }, []);\n    const handleRotate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setState((prev)=>({\n                ...prev,\n                rotation: (prev.rotation + 90) % 360\n            }));\n    }, []);\n    const handlePageChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((page)=>{\n        setState((prev)=>({\n                ...prev,\n                currentPage: Math.max(1, Math.min(page, prev.numPages))\n            }));\n    }, []);\n    const handleToolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((toolId)=>{\n        setState((prev)=>({\n                ...prev,\n                activeTool: toolId\n            }));\n    }, []);\n    const handleDownload = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (state.file) {\n            // For now, just download the original file\n            // In a full implementation, this would save the edited PDF\n            const url = URL.createObjectURL(state.file);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = state.file.name;\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    }, [\n        state.file\n    ]);\n    if (!state.file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"PDF Editor\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Upload a PDF to get started\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        onFileUpload: handleFileUpload\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"PDF Editor\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        state.file.name,\n                                        \" - Page \",\n                                        state.currentPage,\n                                        \" of \",\n                                        state.numPages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleDownload,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Download\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        tools: tools,\n                        onToolSelect: handleToolSelect,\n                        onZoomIn: handleZoomIn,\n                        onZoomOut: handleZoomOut,\n                        onRotate: handleRotate,\n                        scale: state.scale\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-4 p-4 bg-gray-100 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handlePageChange(state.currentPage - 1),\n                                        disabled: state.currentPage <= 1,\n                                        className: \"p-2 rounded-lg bg-white border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: [\n                                            \"Page \",\n                                            state.currentPage,\n                                            \" of \",\n                                            state.numPages\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handlePageChange(state.currentPage + 1),\n                                        disabled: state.currentPage >= state.numPages,\n                                        className: \"p-2 rounded-lg bg-white border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Circle_Download_FileText_Highlighter_Image_Square_Type_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PDFViewer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                file: state.file,\n                                currentPage: state.currentPage,\n                                scale: state.scale,\n                                rotation: state.rotation,\n                                activeTool: state.activeTool,\n                                onDocumentLoadSuccess: onDocumentLoadSuccess\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFEditor.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PDFEditor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PDFEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PDFViewer.tsx":
/*!**************************************!*\
  !*** ./src/components/PDFViewer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_pdf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-pdf */ \"(ssr)/./node_modules/react-pdf/dist/esm/Document.js\");\n/* harmony import */ var react_pdf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-pdf */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page.js\");\n/* harmony import */ var react_pdf_dist_Page_AnnotationLayer_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-pdf/dist/Page/AnnotationLayer.css */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css\");\n/* harmony import */ var react_pdf_dist_Page_TextLayer_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-pdf/dist/Page/TextLayer.css */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst PDFViewer = ({ file, currentPage, scale, rotation, activeTool, onDocumentLoadSuccess })=>{\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [pageWidth, setPageWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageHeight, setPageHeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const onPageLoadSuccess = (page)=>{\n        setPageWidth(page.width);\n        setPageHeight(page.height);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-auto bg-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: containerRef,\n                className: \"relative bg-white shadow-lg\",\n                style: {\n                    transform: `rotate(${rotation}deg)`,\n                    transformOrigin: \"center center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_pdf__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        file: file,\n                        onLoadSuccess: onDocumentLoadSuccess,\n                        loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-96\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0),\n                        error: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-96 text-red-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Failed to load PDF. Please try again.\"\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_pdf__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            pageNumber: currentPage,\n                            scale: scale,\n                            onLoadSuccess: onPageLoadSuccess,\n                            loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-96\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFViewer.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFViewer.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-full pointer-events-none\",\n                        children: activeTool !== \"select\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded text-sm\",\n                            children: [\n                                \"Active Tool: \",\n                                activeTool\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFViewer.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFViewer.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFViewer.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFViewer.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/PDFViewer.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PDFViewer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PDFViewer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Toolbar.tsx":
/*!************************************!*\
  !*** ./src/components/Toolbar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_RotateCw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=RotateCw,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_RotateCw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=RotateCw,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_RotateCw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=RotateCw,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-cw.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Toolbar = ({ tools, onToolSelect, onZoomIn, onZoomOut, onRotate, scale })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-16 bg-white border-r border-gray-200 flex flex-col items-center py-4 space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: tools.map((tool)=>{\n                    const IconComponent = tool.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onToolSelect(tool.id),\n                        className: `\n                w-12 h-12 rounded-lg flex items-center justify-center transition-colors\n                ${tool.active ? \"bg-blue-100 text-blue-600 border-2 border-blue-300\" : \"text-gray-600 hover:bg-gray-100 border-2 border-transparent\"}\n              `,\n                        title: tool.name,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 15\n                        }, undefined)\n                    }, tool.id, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-px bg-gray-300 my-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onZoomIn,\n                        className: \"w-12 h-12 rounded-lg flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors\",\n                        title: \"Zoom In\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onZoomOut,\n                        className: \"w-12 h-12 rounded-lg flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors\",\n                        title: \"Zoom Out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-gray-500 font-medium\",\n                            children: [\n                                Math.round(scale * 100),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onRotate,\n                        className: \"w-12 h-12 rounded-lg flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors\",\n                        title: \"Rotate\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/components/Toolbar.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Toolbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Toolbar.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGRmZWRpdC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/Y2VjNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"PDF Editor - View & Edit PDFs Online\",\n    description: \"A powerful web-based PDF editor for viewing, annotating, and editing PDF documents with various tools including text, highlighting, shapes, and more.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/app/layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/var/www/html/klay_tools/pdfedit/src/app/layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFNaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBVyxDQUFDLEVBQUVULCtKQUFlLENBQUMsWUFBWSxDQUFDO3NCQUM5Q0s7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZGZlZGl0Ly4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoe1xuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJQREYgRWRpdG9yIC0gVmlldyAmIEVkaXQgUERGcyBPbmxpbmVcIixcbiAgZGVzY3JpcHRpb246IFwiQSBwb3dlcmZ1bCB3ZWItYmFzZWQgUERGIGVkaXRvciBmb3Igdmlld2luZywgYW5ub3RhdGluZywgYW5kIGVkaXRpbmcgUERGIGRvY3VtZW50cyB3aXRoIHZhcmlvdXMgdG9vbHMgaW5jbHVkaW5nIHRleHQsIGhpZ2hsaWdodGluZywgc2hhcGVzLCBhbmQgbW9yZS5cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gYW50aWFsaWFzZWRgfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/var/www/html/klay_tools/pdfedit/src/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZGZlZGl0Ly4vc3JjL2FwcC9mYXZpY29uLmljbz8xOTM2Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-pdf","vendor-chunks/lucide-react","vendor-chunks/prop-types","vendor-chunks/react-is","vendor-chunks/tiny-invariant","vendor-chunks/merge-refs","vendor-chunks/make-event-props","vendor-chunks/make-cancellable-promise","vendor-chunks/dequal","vendor-chunks/clsx","vendor-chunks/warning","vendor-chunks/pdfjs-dist","vendor-chunks/object-assign"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FSykot-Mac%2Fvar%2Fwww%2Fhtml%2Fklay_tools%2Fpdfedit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();